# نظام إدارة الموظفين 🏢
## Employee Management System

نظام بسيط وفعال لإدارة بيانات الموظفين ومتابعة حالة الإقامة مع إحصائيات مباشرة.

## 🌟 الميزات الرئيسية

### 📊 الإحصائيات التلقائية
- **إجمالي الموظفين**: عدد جميع الموظفين المسجلين
- **إقامات مجددة**: الإقامات السارية لأكثر من 60 يوماً
- **قاربت على الانتهاء**: الإقامات المتبقي عليها أقل من 60 يوماً
- **إقامات منتهية**: الإقامات المنتهية الصلاحية

### 👥 إدارة الموظفين
- إضافة موظفين جدد
- تعديل بيانات الموظفين الحاليين
- حذف الموظفين
- فلترة الموظفين حسب حالة الإقامة

### 📋 البيانات المطلوبة
- **اسم الموظف**
- **الرقم الوظيفي** (فريد)
- **تاريخ إصدار الإقامة**
- **تاريخ انتهاء الإقامة**

### 🎨 واجهة المستخدم
- تصميم عربي متجاوب
- ألوان مميزة لكل حالة إقامة
- سهولة في الاستخدام
- تحديث مباشر للبيانات

## 🛠️ متطلبات التشغيل

### الخادم المحلي
- **PHP 7.4+**
- **MySQL 5.7+** أو **MariaDB 10.3+**
- **Apache** أو **Nginx**

### أو استخدام
- **XAMPP**
- **WAMP**
- **MAMP**
- **Laragon**

## 📥 التثبيت والإعداد

### 1. تحميل الملفات
```bash
# نسخ المشروع إلى مجلد الخادم المحلي
# مثال: C:\xampp\htdocs\employee_management
```

### 2. إعداد قاعدة البيانات

#### أ. إنشاء قاعدة البيانات
1. افتح **phpMyAdmin** أو أي أداة إدارة قواعد البيانات
2. قم بتشغيل ملف `database.sql`
3. أو قم بإنشاء قاعدة بيانات جديدة باسم `employee_management`

#### ب. تحديث إعدادات الاتصال
افتح ملف `config/database.php` وقم بتحديث البيانات:

```php
define('DB_HOST', 'localhost');     // عنوان الخادم
define('DB_NAME', 'employee_management'); // اسم قاعدة البيانات
define('DB_USER', 'root');          // اسم المستخدم
define('DB_PASS', '');              // كلمة المرور
```

### 3. تشغيل النظام
1. تأكد من تشغيل **Apache** و **MySQL**
2. افتح المتصفح واذهب إلى:
   ```
   http://localhost/employee_management
   ```

## 🎯 كيفية الاستخدام

### إضافة موظف جديد
1. انقر على زر "➕ إضافة موظف جديد"
2. املأ جميع البيانات المطلوبة
3. تأكد من أن تاريخ انتهاء الإقامة بعد تاريخ الإصدار
4. انقر "حفظ"

### تعديل بيانات موظف
1. انقر على زر "تعديل" بجانب اسم الموظف
2. قم بتحديث البيانات المطلوبة
3. انقر "حفظ"

### حذف موظف
1. انقر على زر "حذف" بجانب اسم الموظف
2. أكد عملية الحذف

### فلترة الموظفين
استخدم الأزرار في الأعلى لفلترة الموظفين:
- **جميع الموظفين**: عرض جميع الموظفين
- **المجددة**: الإقامات السارية
- **قاربت الانتهاء**: الإقامات التي تنتهي خلال 60 يوماً
- **المنتهية**: الإقامات المنتهية

## 🎨 ألوان حالات الإقامة

| الحالة | اللون | الوصف |
|--------|-------|--------|
| ✅ مجددة | أخضر | إقامة سارية لأكثر من 60 يوماً |
| ⚠️ قاربت الانتهاء | أصفر | إقامة تنتهي خلال 60 يوماً |
| ❌ منتهية | أحمر | إقامة منتهية الصلاحية |

## 🔧 الملفات الرئيسية

```
📁 نظام ادارة المظفين/
├── 📄 index.php                 # الصفحة الرئيسية
├── 📄 database.sql              # ملف قاعدة البيانات
├── 📁 config/
│   └── 📄 database.php          # إعدادات قاعدة البيانات
├── 📁 includes/
│   └── 📄 functions.php         # الدوال المساعدة
├── 📁 ajax/
│   └── 📄 employee_actions.php  # معالجة العمليات
├── 📁 css/
│   └── 📄 style.css             # التنسيقات
└── 📁 js/
    └── 📄 script.js             # JavaScript
```

## 🚀 تطوير النظام

### إضافة ميزات جديدة
- تصدير البيانات إلى Excel
- إرسال تنبيهات عبر البريد الإلكتروني
- تقارير مفصلة
- نسخ احتياطية تلقائية

### تحسينات الأمان
- تشفير كلمات المرور
- صلاحيات المستخدمين
- حماية من SQL Injection
- تسجيل العمليات

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك اقتراحات للتحسين، يمكنك:
- فحص ملف `error.log` في الخادم
- التأكد من إعدادات قاعدة البيانات
- مراجعة صلاحيات الملفات

## 📝 الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتطويره بحرية.

---

**تم تطوير النظام بـ ❤️ لتسهيل إدارة الموظفين**
