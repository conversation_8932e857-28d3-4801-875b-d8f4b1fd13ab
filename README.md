# 🏢 نظام إدارة الموظفين - Firebase

نظام شامل لإدارة الموظفين ومتابعة حالة الإقامة مع دعم Firebase للنشر السحابي.

## ✨ الميزات

### 🔍 **البحث المتقدم**
- البحث بالاسم أو الرقم الوظيفي أو رقم الهوية
- بحث مباشر أثناء الكتابة
- فلترة حسب حالة الإقامة

### 🆔 **إدارة البيانات**
- رقم الهوية الوطنية (10 أرقام)
- تواريخ إصدار وانتهاء الإقامة
- حساب تلقائي لحالة الإقامة
- منع تكرار الأرقام الوظيفية والهويات

### 📊 **الإحصائيات**
- إجمالي الموظفين
- الإقامات المجددة
- الإقامات قاربت الانتهاء
- الإقامات المنتهية

### 📤 **التصدير**
- تصدير CSV للاستخدام في Excel
- تصدير البيانات المفلترة
- أسماء ملفات تتضمن التاريخ

## 🚀 التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5 RTL
- **Database**: Firebase Firestore
- **Hosting**: Firebase Hosting
- **Icons**: Font Awesome 6

## 📋 متطلبات التشغيل

1. **Node.js** (الإصدار 14 أو أحدث)
2. **Firebase CLI**
3. **حساب Firebase**

## 🛠️ التثبيت والإعداد

### 1. تثبيت Firebase CLI
```bash
npm install -g firebase-tools
```

### 2. تسجيل الدخول إلى Firebase
```bash
firebase login
```

### 3. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. انقر "Add project"
3. اختر اسم للمشروع
4. فعل Firestore Database
5. فعل Firebase Hosting

### 4. إعداد المشروع
1. انسخ إعدادات Firebase من Project Settings
2. استبدل الإعدادات في `public/app.js`:

```javascript
const firebaseConfig = {
    apiKey: "your-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id"
};
```

### 5. ربط المشروع المحلي
```bash
firebase use --add
# اختر مشروعك من القائمة
```

### 6. نشر المشروع
```bash
firebase deploy
```

## 📁 هيكل المشروع

```
├── public/
│   ├── index.html          # الصفحة الرئيسية
│   └── app.js             # منطق التطبيق
├── firebase.json          # إعدادات Firebase
├── firestore.rules        # قواعد Firestore
├── firestore.indexes.json # فهارس Firestore
└── README.md             # هذا الملف
```

## 🔧 الاستخدام

### إضافة موظف جديد
1. انقر "إضافة موظف جديد"
2. املأ جميع الحقول المطلوبة
3. تأكد من صحة رقم الهوية (10 أرقام)
4. انقر "حفظ"

### البحث عن موظف
1. اكتب في مربع البحث:
   - اسم الموظف
   - الرقم الوظيفي
   - رقم الهوية
2. النتائج تظهر تلقائياً

### فلترة الموظفين
- **جميع الموظفين**: عرض الكل
- **المجددة**: الإقامات صالحة لأكثر من 60 يوم
- **قاربت الانتهاء**: تنتهي خلال 60 يوم
- **المنتهية**: انتهت بالفعل

### تصدير البيانات
1. انقر "تصدير البيانات"
2. سيتم تحميل ملف CSV
3. يمكن فتحه في Excel

## 🛡️ الأمان

### قواعد Firestore
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /employees/{document} {
      allow read, write: if true; // للتطوير فقط
    }
  }
}
```

**⚠️ تحذير**: القواعد الحالية مفتوحة للتطوير. يجب تحسين الأمان للإنتاج.

## 📊 البيانات التجريبية

يتم إنشاء 5 موظفين تجريبيين تلقائياً عند أول تشغيل:
- أحمد محمد علي (EMP001)
- فاطمة أحمد (EMP002)
- محمد عبدالله (EMP003)
- سارة محمود (EMP004)
- علي حسن (EMP005)

## 🔄 التحديثات المستقبلية

- [ ] نظام مصادقة المستخدمين
- [ ] تقارير PDF محسنة
- [ ] إشعارات انتهاء الإقامة
- [ ] نسخ احتياطية تلقائية
- [ ] واجهة إدارة متقدمة

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل:
1. تحقق من إعدادات Firebase
2. تأكد من تفعيل Firestore
3. راجع وحدة تحكم المطور في المتصفح

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 👨‍💻 المطور

تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والموثوقية.

---

**🚀 استمتع بإدارة الموظفين بكفاءة مع Firebase!**
