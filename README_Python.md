# نظام إدارة الموظفين - Python Flask 🐍
## Employee Management System - Python Flask

نظام بسيط وفعال لإدارة بيانات الموظفين ومتابعة حالة الإقامة باستخدام Python و Flask.

## 🌟 الميزات الرئيسية

### 📊 الإحصائيات التلقائية
- **إجمالي الموظفين**: عدد جميع الموظفين المسجلين
- **إقامات مجددة**: الإقامات السارية لأكثر من 60 يوماً
- **قاربت على الانتهاء**: الإقامات المتبقي عليها أقل من 60 يوماً
- **إقامات منتهية**: الإقامات المنتهية الصلاحية

### 👥 إدارة الموظفين
- إضافة موظفين جدد مع التحقق من صحة البيانات
- تعديل بيانات الموظفين الحاليين
- حذف الموظفين مع تأكيد
- فلترة الموظفين حسب حالة الإقامة

### 📋 البيانات المطلوبة
- **اسم الموظف**
- **الرقم الوظيفي** (فريد)
- **تاريخ إصدار الإقامة**
- **تاريخ انتهاء الإقامة**

### 🎨 واجهة المستخدم
- تصميم عربي متجاوب باستخدام Bootstrap
- ألوان مميزة لكل حالة إقامة
- تحديث مباشر للبيانات بدون إعادة تحميل الصفحة
- واجهة سهلة الاستخدام

## 🛠️ متطلبات التشغيل

### البرامج المطلوبة
- **Python 3.7+**
- **pip** (مدير حزم Python)

### المكتبات المطلوبة (ستُثبت تلقائياً)
- Flask 2.3.3
- SQLAlchemy 2.0.23
- Flask-SQLAlchemy 3.1.1
- python-dateutil 2.8.2

## 🚀 التثبيت والتشغيل

### الطريقة الأولى: التشغيل السريع
```bash
# تشغيل النظام مباشرة
python run.py
```

### الطريقة الثانية: التشغيل اليدوي

#### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 2. تشغيل التطبيق
```bash
python app.py
```

#### 3. فتح الموقع
افتح المتصفح واذهب إلى:
```
http://localhost:5000
```

## 📁 هيكل المشروع

```
📁 نظام ادارة المظفين/
├── 📄 app.py                    # التطبيق الرئيسي
├── 📄 models.py                 # نماذج قاعدة البيانات
├── 📄 database.py               # إعداد قاعدة البيانات
├── 📄 run.py                    # ملف التشغيل السريع
├── 📄 requirements.txt          # المتطلبات
├── 📄 employees.db              # قاعدة البيانات (تُنشأ تلقائياً)
├── 📁 templates/
│   ├── 📄 base.html            # القالب الأساسي
│   └── 📄 index.html           # الصفحة الرئيسية
└── 📁 static/ (اختياري)
    ├── 📁 css/
    ├── 📁 js/
    └── 📁 images/
```

## 🎯 كيفية الاستخدام

### إضافة موظف جديد
1. انقر على زر "➕ إضافة موظف جديد"
2. املأ جميع البيانات المطلوبة:
   - اسم الموظف
   - الرقم الوظيفي (يجب أن يكون فريد)
   - تاريخ إصدار الإقامة
   - تاريخ انتهاء الإقامة
3. تأكد من أن تاريخ انتهاء الإقامة بعد تاريخ الإصدار
4. انقر "حفظ"

### تعديل بيانات موظف
1. انقر على زر "تعديل" بجانب اسم الموظف
2. قم بتحديث البيانات المطلوبة
3. انقر "حفظ"

### حذف موظف
1. انقر على زر "حذف" بجانب اسم الموظف
2. أكد عملية الحذف في النافذة المنبثقة

### فلترة الموظفين
استخدم الأزرار في الأعلى لفلترة الموظفين:
- **جميع الموظفين**: عرض جميع الموظفين
- **المجددة**: الإقامات السارية لأكثر من 60 يوماً
- **قاربت الانتهاء**: الإقامات التي تنتهي خلال 60 يوماً
- **المنتهية**: الإقامات المنتهية الصلاحية

## 🎨 ألوان حالات الإقامة

| الحالة | اللون | الوصف |
|--------|-------|--------|
| ✅ مجددة | أخضر | إقامة سارية لأكثر من 60 يوماً |
| ⚠️ قاربت الانتهاء | أصفر | إقامة تنتهي خلال 60 يوماً |
| ❌ منتهية | أحمر | إقامة منتهية الصلاحية |

## 🔧 الميزات التقنية

### قاعدة البيانات
- **SQLite**: قاعدة بيانات محلية لا تحتاج إعداد
- **SQLAlchemy**: ORM قوي لإدارة قاعدة البيانات
- **بيانات تجريبية**: تُنشأ تلقائياً عند التشغيل الأول

### الواجهة الأمامية
- **Bootstrap 5**: تصميم متجاوب وجميل
- **jQuery**: تفاعل سلس مع الخادم
- **Font Awesome**: أيقونات جميلة
- **AJAX**: تحديث البيانات بدون إعادة تحميل

### الخادم الخلفي
- **Flask**: إطار عمل Python سريع وبسيط
- **RESTful API**: واجهة برمجية منظمة
- **JSON**: تبادل البيانات بصيغة JSON
- **معالجة الأخطاء**: رسائل خطأ واضحة

## 🚀 تطوير النظام

### إضافة ميزات جديدة
- تصدير البيانات إلى Excel/CSV
- إرسال تنبيهات عبر البريد الإلكتروني
- تقارير مفصلة ورسوم بيانية
- نسخ احتياطية تلقائية
- نظام المستخدمين والصلاحيات

### تحسينات الأداء
- فهرسة قاعدة البيانات
- تخزين مؤقت للبيانات
- ضغط الاستجابات
- تحسين الاستعلامات

## 🔒 الأمان

### الميزات الحالية
- التحقق من صحة البيانات
- حماية من SQL Injection
- تشفير الاتصالات (HTTPS في الإنتاج)
- معالجة آمنة للأخطاء

### تحسينات مقترحة
- نظام تسجيل الدخول
- تشفير كلمات المرور
- صلاحيات المستخدمين
- تسجيل العمليات (Audit Log)

## 🐛 حل المشاكل الشائعة

### خطأ في تثبيت المتطلبات
```bash
# تحديث pip أولاً
python -m pip install --upgrade pip

# ثم تثبيت المتطلبات
pip install -r requirements.txt
```

### خطأ في الوصول للموقع
1. تأكد من تشغيل التطبيق بنجاح
2. تحقق من عدم استخدام المنفذ 5000 من برنامج آخر
3. جرب فتح: `http://127.0.0.1:5000`

### خطأ في قاعدة البيانات
1. احذف ملف `employees.db`
2. أعد تشغيل التطبيق لإنشاء قاعدة بيانات جديدة

### مشاكل الترميز العربي
- تأكد من حفظ الملفات بترميز UTF-8
- تحقق من إعدادات المتصفح للترميز

## 📞 الدعم والمساعدة

### للحصول على المساعدة
1. تحقق من رسائل الخطأ في Terminal/Command Prompt
2. راجع ملف `app.py` للتأكد من عدم وجود أخطاء
3. تأكد من تثبيت جميع المتطلبات بنجاح

### معلومات إضافية
- **المنفذ الافتراضي**: 5000
- **قاعدة البيانات**: SQLite (employees.db)
- **الترميز**: UTF-8
- **المتصفحات المدعومة**: جميع المتصفحات الحديثة

## 📝 الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتطويره بحرية.

---

**🎉 تم تطوير النظام بـ ❤️ باستخدام Python و Flask لتسهيل إدارة الموظفين**

### 🚀 للبدء السريع:
```bash
python run.py
```

ثم افتح: **http://localhost:5000**
