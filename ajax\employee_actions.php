<?php
/**
 * معالجة عمليات الموظفين
 * Employee Actions Handler
 */

header('Content-Type: application/json; charset=utf-8');
require_once '../includes/functions.php';

// التحقق من طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

$response = ['success' => false, 'message' => '', 'data' => null];

try {
    switch ($action) {
        case 'add':
            if ($method === 'POST') {
                $data = [
                    'name' => trim($_POST['name']),
                    'employee_number' => trim($_POST['employee_number']),
                    'residence_issue_date' => $_POST['residence_issue_date'],
                    'residence_expiry_date' => $_POST['residence_expiry_date']
                ];
                
                // التحقق من صحة البيانات
                if (empty($data['name']) || empty($data['employee_number']) || 
                    empty($data['residence_issue_date']) || empty($data['residence_expiry_date'])) {
                    throw new Exception('جميع الحقول مطلوبة');
                }
                
                // التحقق من أن تاريخ الانتهاء بعد تاريخ الإصدار
                if (strtotime($data['residence_expiry_date']) <= strtotime($data['residence_issue_date'])) {
                    throw new Exception('تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار');
                }
                
                if (addEmployee($data)) {
                    $response['success'] = true;
                    $response['message'] = 'تم إضافة الموظف بنجاح';
                } else {
                    throw new Exception('فشل في إضافة الموظف');
                }
            }
            break;
            
        case 'update':
            if ($method === 'POST') {
                $id = $_POST['id'];
                $data = [
                    'name' => trim($_POST['name']),
                    'employee_number' => trim($_POST['employee_number']),
                    'residence_issue_date' => $_POST['residence_issue_date'],
                    'residence_expiry_date' => $_POST['residence_expiry_date']
                ];
                
                // التحقق من صحة البيانات
                if (empty($data['name']) || empty($data['employee_number']) || 
                    empty($data['residence_issue_date']) || empty($data['residence_expiry_date'])) {
                    throw new Exception('جميع الحقول مطلوبة');
                }
                
                // التحقق من أن تاريخ الانتهاء بعد تاريخ الإصدار
                if (strtotime($data['residence_expiry_date']) <= strtotime($data['residence_issue_date'])) {
                    throw new Exception('تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار');
                }
                
                if (updateEmployee($id, $data)) {
                    $response['success'] = true;
                    $response['message'] = 'تم تحديث بيانات الموظف بنجاح';
                } else {
                    throw new Exception('فشل في تحديث بيانات الموظف');
                }
            }
            break;
            
        case 'delete':
            if ($method === 'POST') {
                $id = $_POST['id'];
                
                if (deleteEmployee($id)) {
                    $response['success'] = true;
                    $response['message'] = 'تم حذف الموظف بنجاح';
                } else {
                    throw new Exception('فشل في حذف الموظف');
                }
            }
            break;
            
        case 'get':
            if ($method === 'GET') {
                $id = $_GET['id'];
                $employee = getEmployeeById($id);
                
                if ($employee) {
                    $response['success'] = true;
                    $response['data'] = $employee;
                } else {
                    throw new Exception('الموظف غير موجود');
                }
            }
            break;
            
        case 'list':
            if ($method === 'GET') {
                $filter = $_GET['filter'] ?? 'all';
                $employees = getAllEmployees($filter);
                
                $response['success'] = true;
                $response['data'] = $employees;
            }
            break;
            
        case 'statistics':
            if ($method === 'GET') {
                $stats = getEmployeeStatistics();
                
                $response['success'] = true;
                $response['data'] = $stats;
            }
            break;
            
        default:
            throw new Exception('عملية غير صحيحة');
    }
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = $e->getMessage();
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
