"""
نظام إدارة الموظفين - Flask Application
Employee Management System - Flask Application
"""

from flask import Flask, render_template, request, jsonify
from models import db, Employee
from database import init_database
from datetime import datetime, date
import os

# إنشاء التطبيق
app = Flask(__name__)

# إعدادات التطبيق
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///employees.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db.init_app(app)

# تهيئة قاعدة البيانات مع البيانات التجريبية
init_database(app)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    statistics = Employee.get_statistics()
    return render_template('index.html', statistics=statistics)

@app.route('/api/employees', methods=['GET'])
def get_employees():
    """جلب قائمة الموظفين"""
    try:
        status_filter = request.args.get('filter', 'all')
        employees = Employee.get_by_status(status_filter)
        statistics = Employee.get_statistics()
        
        employees_data = [emp.to_dict() for emp in employees]
        
        return jsonify({
            'success': True,
            'employees': employees_data,
            'statistics': statistics
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب البيانات: {str(e)}'
        }), 500

@app.route('/api/employees/<int:employee_id>', methods=['GET'])
def get_employee(employee_id):
    """جلب بيانات موظف محدد"""
    try:
        employee = Employee.query.get_or_404(employee_id)
        
        return jsonify({
            'success': True,
            'employee': employee.to_dict()
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'الموظف غير موجود: {str(e)}'
        }), 404

@app.route('/api/employees', methods=['POST'])
def add_employee():
    """إضافة موظف جديد"""
    try:
        data = request.get_json()
        
        # التحقق من صحة البيانات
        required_fields = ['name', 'employee_number', 'residence_issue_date', 'residence_expiry_date']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                }), 400
        
        # التحقق من عدم تكرار الرقم الوظيفي
        existing_employee = Employee.query.filter_by(employee_number=data['employee_number']).first()
        if existing_employee:
            return jsonify({
                'success': False,
                'message': 'الرقم الوظيفي موجود مسبقاً'
            }), 400
        
        # تحويل التواريخ
        issue_date = datetime.strptime(data['residence_issue_date'], '%Y-%m-%d').date()
        expiry_date = datetime.strptime(data['residence_expiry_date'], '%Y-%m-%d').date()
        
        # التحقق من أن تاريخ الانتهاء بعد تاريخ الإصدار
        if expiry_date <= issue_date:
            return jsonify({
                'success': False,
                'message': 'تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار'
            }), 400
        
        # إنشاء الموظف الجديد
        employee = Employee(
            name=data['name'].strip(),
            employee_number=data['employee_number'].strip(),
            residence_issue_date=issue_date,
            residence_expiry_date=expiry_date
        )
        
        db.session.add(employee)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إضافة الموظف بنجاح',
            'employee': employee.to_dict()
        }), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في إضافة الموظف: {str(e)}'
        }), 500

@app.route('/api/employees/<int:employee_id>', methods=['PUT'])
def update_employee(employee_id):
    """تحديث بيانات موظف"""
    try:
        employee = Employee.query.get_or_404(employee_id)
        data = request.get_json()

        # التحقق من صحة البيانات
        required_fields = ['name', 'employee_number', 'residence_issue_date', 'residence_expiry_date']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب'
                }), 400

        # التحقق من عدم تكرار الرقم الوظيفي (إلا إذا كان نفس الموظف)
        existing_employee = Employee.query.filter_by(employee_number=data['employee_number']).first()
        if existing_employee and existing_employee.id != employee_id:
            return jsonify({
                'success': False,
                'message': 'الرقم الوظيفي موجود مسبقاً'
            }), 400

        # تحويل التواريخ
        issue_date = datetime.strptime(data['residence_issue_date'], '%Y-%m-%d').date()
        expiry_date = datetime.strptime(data['residence_expiry_date'], '%Y-%m-%d').date()

        # التحقق من أن تاريخ الانتهاء بعد تاريخ الإصدار
        if expiry_date <= issue_date:
            return jsonify({
                'success': False,
                'message': 'تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار'
            }), 400

        # تحديث البيانات
        employee.name = data['name'].strip()
        employee.employee_number = data['employee_number'].strip()
        employee.residence_issue_date = issue_date
        employee.residence_expiry_date = expiry_date
        employee.updated_at = datetime.utcnow()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحديث بيانات الموظف بنجاح',
            'employee': employee.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في تحديث الموظف: {str(e)}'
        }), 500

@app.route('/api/employees/<int:employee_id>', methods=['DELETE'])
def delete_employee(employee_id):
    """حذف موظف"""
    try:
        employee = Employee.query.get_or_404(employee_id)

        db.session.delete(employee)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف الموظف بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في حذف الموظف: {str(e)}'
        }), 500

@app.route('/api/statistics')
def get_statistics():
    """جلب الإحصائيات"""
    try:
        statistics = Employee.get_statistics()
        return jsonify({
            'success': True,
            'statistics': statistics
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الإحصائيات: {str(e)}'
        }), 500

@app.errorhandler(404)
def not_found(error):
    """صفحة الخطأ 404"""
    return jsonify({
        'success': False,
        'message': 'الصفحة غير موجودة'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة الخطأ 500"""
    db.session.rollback()
    return jsonify({
        'success': False,
        'message': 'خطأ داخلي في الخادم'
    }), 500

if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام إدارة الموظفين...")
    print("📊 الموقع متاح على: http://localhost:5000")
    print("🔧 للإيقاف: اضغط Ctrl+C")

    app.run(debug=True, host='0.0.0.0', port=5000)
