"""
إعداد قاعدة البيانات والبيانات التجريبية
Database Setup and Sample Data
"""

from models import db, Employee
from datetime import date, datetime
from dateutil.relativedelta import relativedelta

def init_database(app):
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        # التحقق من وجود بيانات
        if Employee.query.count() == 0:
            create_sample_data()

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    
    # تاريخ اليوم
    today = date.today()
    
    # بيانات تجريبية
    sample_employees = [
        {
            'name': 'أحمد محمد علي',
            'employee_number': 'EMP001',
            'residence_issue_date': date(2023, 1, 15),
            'residence_expiry_date': date(2025, 1, 15)  # مجددة
        },
        {
            'name': 'فاطمة أحمد',
            'employee_number': 'EMP002',
            'residence_issue_date': date(2023, 6, 10),
            'residence_expiry_date': today + relativedelta(days=30)  # قاربت الانتهاء
        },
        {
            'name': 'محمد عبدالله',
            'employee_number': 'EMP003',
            'residence_issue_date': date(2024, 3, 20),
            'residence_expiry_date': date(2025, 3, 20)  # مجددة
        },
        {
            'name': 'سارة محمود',
            'employee_number': 'EMP004',
            'residence_issue_date': date(2022, 8, 5),
            'residence_expiry_date': today - relativedelta(days=30)  # منتهية
        },
        {
            'name': 'علي حسن',
            'employee_number': 'EMP005',
            'residence_issue_date': date(2024, 1, 1),
            'residence_expiry_date': date(2026, 1, 1)  # مجددة
        },
        {
            'name': 'نور الدين',
            'employee_number': 'EMP006',
            'residence_issue_date': date(2023, 12, 15),
            'residence_expiry_date': today + relativedelta(days=45)  # قاربت الانتهاء
        },
        {
            'name': 'ليلى عبدالرحمن',
            'employee_number': 'EMP007',
            'residence_issue_date': date(2024, 5, 10),
            'residence_expiry_date': date(2025, 5, 10)  # مجددة
        },
        {
            'name': 'خالد أحمد',
            'employee_number': 'EMP008',
            'residence_issue_date': date(2022, 12, 1),
            'residence_expiry_date': today - relativedelta(days=60)  # منتهية
        }
    ]
    
    # إضافة البيانات
    for emp_data in sample_employees:
        employee = Employee(
            name=emp_data['name'],
            employee_number=emp_data['employee_number'],
            residence_issue_date=emp_data['residence_issue_date'],
            residence_expiry_date=emp_data['residence_expiry_date']
        )
        db.session.add(employee)
    
    # حفظ التغييرات
    db.session.commit()
    print("✅ تم إنشاء البيانات التجريبية بنجاح!")

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    db.drop_all()
    db.create_all()
    create_sample_data()
    print("🔄 تم إعادة تعيين قاعدة البيانات!")
