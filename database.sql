-- قاعدة بيانات نظام إدارة الموظفين
-- Employee Management System Database

CREATE DATABASE IF NOT EXISTS employee_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE employee_management;

-- جدول الموظفين
CREATE TABLE IF NOT EXISTS employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'اسم الموظف',
    employee_number VARCHAR(50) UNIQUE NOT NULL COMMENT 'الرقم الوظيفي',
    residence_issue_date DATE NOT NULL COMMENT 'تاريخ إصدار الإقامة',
    residence_expiry_date DATE NOT NULL COMMENT 'تاريخ انتهاء الإقامة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات تجريبية
INSERT INTO employees (name, employee_number, residence_issue_date, residence_expiry_date) VALUES
('أحمد محمد علي', 'EMP001', '2023-01-15', '2025-01-15'),
('فاطمة أحمد', 'EMP002', '2023-06-10', '2024-12-10'),
('محمد عبدالله', 'EMP003', '2024-03-20', '2025-03-20'),
('سارة محمود', 'EMP004', '2022-08-05', '2024-08-05'),
('علي حسن', 'EMP005', '2024-01-01', '2026-01-01'),
('نور الدين', 'EMP006', '2023-12-15', '2024-11-15'),
('ليلى عبدالرحمن', 'EMP007', '2024-05-10', '2025-05-10');

-- فهرس لتحسين الأداء
CREATE INDEX idx_employee_number ON employees(employee_number);
CREATE INDEX idx_expiry_date ON employees(residence_expiry_date);
