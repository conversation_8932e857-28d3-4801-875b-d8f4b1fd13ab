<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين - عرض توضيحي</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <!-- الرأس -->
        <div class="header">
            <h1>🏢 نظام إدارة الموظفين</h1>
            <p>متابعة حالة الإقامة والبيانات الأساسية للموظفين</p>
            <div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong>📋 عرض توضيحي:</strong> هذا عرض للواجهة فقط. لتشغيل النظام كاملاً تحتاج إلى تثبيت XAMPP أو WAMP
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="statistics">
            <div class="stat-card stat-total">
                <div class="icon">👥</div>
                <div class="number">7</div>
                <div class="label">إجمالي الموظفين</div>
            </div>
            
            <div class="stat-card stat-renewed">
                <div class="icon">✅</div>
                <div class="number">3</div>
                <div class="label">إقامات مجددة</div>
            </div>
            
            <div class="stat-card stat-expiring">
                <div class="icon">⚠️</div>
                <div class="number">2</div>
                <div class="label">قاربت على الانتهاء</div>
            </div>
            
            <div class="stat-card stat-expired">
                <div class="icon">❌</div>
                <div class="number">2</div>
                <div class="label">إقامات منتهية</div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="controls">
            <div>
                <button class="btn btn-primary filter-btn" data-filter="all">جميع الموظفين</button>
                <button class="btn btn-secondary filter-btn" data-filter="renewed">المجددة</button>
                <button class="btn btn-secondary filter-btn" data-filter="expiring">قاربت الانتهاء</button>
                <button class="btn btn-secondary filter-btn" data-filter="expired">المنتهية</button>
            </div>
            
            <div>
                <button class="btn btn-success" onclick="alert('يتطلب تشغيل النظام الكامل مع PHP و MySQL')">
                    ➕ إضافة موظف جديد
                </button>
            </div>
        </div>

        <!-- جدول الموظفين -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم الموظف</th>
                        <th>الرقم الوظيفي</th>
                        <th>تاريخ إصدار الإقامة</th>
                        <th>تاريخ انتهاء الإقامة</th>
                        <th>حالة الإقامة</th>
                        <th>تاريخ الإضافة</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>أحمد محمد علي</td>
                        <td>EMP001</td>
                        <td>2023-01-15</td>
                        <td>2025-01-15</td>
                        <td><span class="status-badge status-renewed">✅ مجددة</span></td>
                        <td>2023-01-15</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="alert('يتطلب PHP و MySQL')">تعديل</button>
                            <button class="btn btn-danger btn-sm" onclick="alert('يتطلب PHP و MySQL')">حذف</button>
                        </td>
                    </tr>
                    <tr>
                        <td>فاطمة أحمد</td>
                        <td>EMP002</td>
                        <td>2023-06-10</td>
                        <td>2024-12-10</td>
                        <td><span class="status-badge status-expiring">⚠️ قاربت على الانتهاء</span></td>
                        <td>2023-06-10</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="alert('يتطلب PHP و MySQL')">تعديل</button>
                            <button class="btn btn-danger btn-sm" onclick="alert('يتطلب PHP و MySQL')">حذف</button>
                        </td>
                    </tr>
                    <tr>
                        <td>محمد عبدالله</td>
                        <td>EMP003</td>
                        <td>2024-03-20</td>
                        <td>2025-03-20</td>
                        <td><span class="status-badge status-renewed">✅ مجددة</span></td>
                        <td>2024-03-20</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="alert('يتطلب PHP و MySQL')">تعديل</button>
                            <button class="btn btn-danger btn-sm" onclick="alert('يتطلب PHP و MySQL')">حذف</button>
                        </td>
                    </tr>
                    <tr>
                        <td>سارة محمود</td>
                        <td>EMP004</td>
                        <td>2022-08-05</td>
                        <td>2024-08-05</td>
                        <td><span class="status-badge status-expired">❌ منتهية</span></td>
                        <td>2022-08-05</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="alert('يتطلب PHP و MySQL')">تعديل</button>
                            <button class="btn btn-danger btn-sm" onclick="alert('يتطلب PHP و MySQL')">حذف</button>
                        </td>
                    </tr>
                    <tr>
                        <td>علي حسن</td>
                        <td>EMP005</td>
                        <td>2024-01-01</td>
                        <td>2026-01-01</td>
                        <td><span class="status-badge status-renewed">✅ مجددة</span></td>
                        <td>2024-01-01</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="alert('يتطلب PHP و MySQL')">تعديل</button>
                            <button class="btn btn-danger btn-sm" onclick="alert('يتطلب PHP و MySQL')">حذف</button>
                        </td>
                    </tr>
                    <tr>
                        <td>نور الدين</td>
                        <td>EMP006</td>
                        <td>2023-12-15</td>
                        <td>2024-11-15</td>
                        <td><span class="status-badge status-expiring">⚠️ قاربت على الانتهاء</span></td>
                        <td>2023-12-15</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="alert('يتطلب PHP و MySQL')">تعديل</button>
                            <button class="btn btn-danger btn-sm" onclick="alert('يتطلب PHP و MySQL')">حذف</button>
                        </td>
                    </tr>
                    <tr>
                        <td>ليلى عبدالرحمن</td>
                        <td>EMP007</td>
                        <td>2024-05-10</td>
                        <td>2025-05-10</td>
                        <td><span class="status-badge status-renewed">✅ مجددة</span></td>
                        <td>2024-05-10</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="alert('يتطلب PHP و MySQL')">تعديل</button>
                            <button class="btn btn-danger btn-sm" onclick="alert('يتطلب PHP و MySQL')">حذف</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- تعليمات التثبيت -->
        <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-top: 30px;">
            <h2>🚀 خطوات تشغيل النظام الكامل:</h2>
            <ol style="text-align: right; line-height: 2;">
                <li><strong>تحميل XAMPP:</strong> <a href="https://www.apachefriends.org/download.html" target="_blank">https://www.apachefriends.org/download.html</a></li>
                <li><strong>تثبيت XAMPP</strong> واختيار Apache و MySQL</li>
                <li><strong>تشغيل XAMPP Control Panel</strong> وتفعيل Apache و MySQL</li>
                <li><strong>نسخ مجلد المشروع</strong> إلى <code>C:\xampp\htdocs\</code></li>
                <li><strong>فتح phpMyAdmin:</strong> <a href="http://localhost/phpmyadmin" target="_blank">http://localhost/phpmyadmin</a></li>
                <li><strong>تشغيل ملف database.sql</strong> لإنشاء قاعدة البيانات</li>
                <li><strong>فتح النظام:</strong> <a href="http://localhost/نظام ادارة المظفين" target="_blank">http://localhost/نظام ادارة المظفين</a></li>
            </ol>
        </div>
    </div>
</body>
</html>
