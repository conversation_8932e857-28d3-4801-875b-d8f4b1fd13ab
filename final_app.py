"""
نظام إدارة الموظفين النهائي - مع تصدير PDF
Final Employee Management System - With PDF Export
"""

from flask import Flask, render_template_string, request, jsonify, make_response
import sqlite3
import json
from datetime import datetime, date
from dateutil.relativedelta import relativedelta
import io
import csv

app = Flask(__name__)

# HTML Template النهائي
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين النهائي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container { padding: 20px 0; }
        .header-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            padding: 30px;
            text-align: center;
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .stat-card:hover { transform: translateY(-5px); }
        .stat-icon { font-size: 3rem; margin-bottom: 15px; }
        .stat-number { font-size: 2.5rem; font-weight: bold; margin-bottom: 10px; }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        .table th { background: #f8f9fa; font-weight: bold; }
        .table tbody tr:hover { background: #f8f9fa; }
        .search-highlight { background-color: yellow; font-weight: bold; }
        
        /* تنسيقات الطباعة */
        @media print {
            body { background: white !important; }
            .no-print { display: none !important; }
            .content-card { box-shadow: none !important; border: 1px solid #ddd; }
            .header-card { box-shadow: none !important; border: 1px solid #ddd; }
        }
    </style>
</head>
<body>
    <div class="container main-container">
        <!-- الرأس -->
        <div class="header-card">
            <h1><i class="fas fa-building"></i> نظام إدارة الموظفين النهائي</h1>
            <p class="lead">متابعة حالة الإقامة والبيانات الأساسية للموظفين مع البحث والتصدير الشامل</p>
        </div>

        <!-- رسائل التنبيه -->
        <div id="alertContainer"></div>

        <!-- الإحصائيات -->
        <div class="row" id="statisticsRow">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number text-primary" id="totalCount">{{ statistics.total }}</div>
                    <div class="stat-label">إجمالي الموظفين</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-number text-success" id="renewedCount">{{ statistics.renewed }}</div>
                    <div class="stat-label">إقامات مجددة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-number text-warning" id="expiringCount">{{ statistics.expiring }}</div>
                    <div class="stat-label">قاربت على الانتهاء</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">❌</div>
                    <div class="stat-number text-danger" id="expiredCount">{{ statistics.expired }}</div>
                    <div class="stat-label">إقامات منتهية</div>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم والبحث -->
        <div class="content-card no-print">
            <div class="row align-items-center mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو الرقم الوظيفي أو رقم الهوية..." value="{{ request.args.get('search', '') }}">
                        <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#employeeModal">
                        <i class="fas fa-plus"></i> إضافة موظف جديد
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                                <i class="fas fa-file-csv text-success"></i> تصدير CSV
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf text-danger"></i> تصدير PDF
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="printReport()">
                                <i class="fas fa-print text-primary"></i> طباعة التقرير
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-primary filter-btn active" data-filter="all">جميع الموظفين</button>
                        <button type="button" class="btn btn-outline-success filter-btn" data-filter="renewed">المجددة</button>
                        <button type="button" class="btn btn-outline-warning filter-btn" data-filter="expiring">قاربت الانتهاء</button>
                        <button type="button" class="btn btn-outline-danger filter-btn" data-filter="expired">المنتهية</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الموظفين -->
        <div class="content-card">
            <div class="table-responsive">
                <table class="table table-hover" id="employeesTable">
                    <thead>
                        <tr>
                            <th>اسم الموظف</th>
                            <th>الرقم الوظيفي</th>
                            <th>رقم الهوية</th>
                            <th>تاريخ إصدار الإقامة</th>
                            <th>تاريخ انتهاء الإقامة</th>
                            <th>حالة الإقامة</th>
                            <th class="no-print">العمليات</th>
                        </tr>
                    </thead>
                    <tbody id="employeesTableBody">
                        {% for employee in employees %}
                        <tr>
                            <td><strong>{{ employee.name }}</strong></td>
                            <td><code>{{ employee.employee_number }}</code></td>
                            <td><code>{{ employee.national_id }}</code></td>
                            <td>{{ employee.residence_issue_date }}</td>
                            <td>{{ employee.residence_expiry_date }}</td>
                            <td>
                                <span class="badge bg-{{ employee.status_info.class }}">
                                    {{ employee.status_info.icon }} {{ employee.status_info.label }}
                                </span>
                            </td>
                            <td class="no-print">
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editEmployee({{ employee.id }})">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteEmployee({{ employee.id }})">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- نموذج إضافة/تعديل الموظف -->
        <div class="modal fade" id="employeeModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalTitle">إضافة موظف جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="employeeForm">
                            <input type="hidden" id="employeeId" name="id">
                            <div class="mb-3">
                                <label for="employeeName" class="form-label">اسم الموظف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="employeeName" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="employeeNumber" class="form-label">الرقم الوظيفي <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="employeeNumber" name="employee_number" required>
                            </div>
                            <div class="mb-3">
                                <label for="nationalId" class="form-label">رقم الهوية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nationalId" name="national_id" required 
                                       placeholder="مثال: 1234567890" maxlength="10" pattern="[0-9]{10}">
                                <div class="form-text">يجب أن يكون رقم الهوية مكون من 10 أرقام</div>
                            </div>
                            <div class="mb-3">
                                <label for="issueDate" class="form-label">تاريخ إصدار الإقامة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="issueDate" name="residence_issue_date" required>
                            </div>
                            <div class="mb-3">
                                <label for="expiryDate" class="form-label">تاريخ انتهاء الإقامة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="expiryDate" name="residence_expiry_date" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" id="saveEmployeeBtn">حفظ</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        let currentFilter = 'all';
        let editingEmployeeId = null;
        let searchTimeout = null;

        $(document).ready(function() {
            setupEventListeners();
            updateFilterFromURL();
        });

        function setupEventListeners() {
            $('.filter-btn').click(function() {
                currentFilter = $(this).data('filter');
                updateFilterButtons();
                loadEmployees();
            });
            
            $('#saveEmployeeBtn').click(function() {
                saveEmployee();
            });
            
            $('#employeeModal').on('hidden.bs.modal', function() {
                resetForm();
            });
            
            // البحث المباشر
            $('#searchInput').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    loadEmployees();
                }, 500);
            });
            
            // مسح البحث
            $('#clearSearch').click(function() {
                $('#searchInput').val('');
                loadEmployees();
            });
            
            // التحقق من رقم الهوية
            $('#nationalId').on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                $(this).val(value);
                
                if (value.length === 10) {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                } else if (value.length > 0) {
                    $(this).removeClass('is-valid').addClass('is-invalid');
                } else {
                    $(this).removeClass('is-valid is-invalid');
                }
            });
        }

        function updateFilterFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const filter = urlParams.get('filter') || 'all';
            currentFilter = filter;
            updateFilterButtons();
        }

        function updateFilterButtons() {
            $('.filter-btn').removeClass('btn-primary btn-outline-success btn-outline-warning btn-outline-danger active');
            const activeBtn = $(`.filter-btn[data-filter="${currentFilter}"]`);

            if (currentFilter === 'all') {
                activeBtn.addClass('btn-primary active');
            } else if (currentFilter === 'renewed') {
                activeBtn.addClass('btn-success active');
            } else if (currentFilter === 'expiring') {
                activeBtn.addClass('btn-warning active');
            } else if (currentFilter === 'expired') {
                activeBtn.addClass('btn-danger active');
            }

            $('.filter-btn').not(activeBtn).each(function() {
                const filter = $(this).data('filter');
                if (filter === 'renewed') {
                    $(this).addClass('btn-outline-success');
                } else if (filter === 'expiring') {
                    $(this).addClass('btn-outline-warning');
                } else if (filter === 'expired') {
                    $(this).addClass('btn-outline-danger');
                } else {
                    $(this).addClass('btn-outline-primary');
                }
            });
        }

        function loadEmployees() {
            const searchQuery = $('#searchInput').val();
            let url = `/?filter=${currentFilter}`;
            if (searchQuery) {
                url += `&search=${encodeURIComponent(searchQuery)}`;
            }
            window.location.href = url;
        }

        function editEmployee(employeeId) {
            $.get(`/api/employees/${employeeId}`)
                .done(function(data) {
                    if (data.success) {
                        const employee = data.employee;
                        editingEmployeeId = employeeId;

                        $('#modalTitle').text('تعديل بيانات الموظف');
                        $('#employeeId').val(employee.id);
                        $('#employeeName').val(employee.name);
                        $('#employeeNumber').val(employee.employee_number);
                        $('#nationalId').val(employee.national_id);
                        $('#issueDate').val(employee.residence_issue_date);
                        $('#expiryDate').val(employee.residence_expiry_date);

                        $('#employeeModal').modal('show');
                    } else {
                        showAlert('خطأ في تحميل بيانات الموظف: ' + data.message, 'danger');
                    }
                });
        }

        function saveEmployee() {
            const formData = {
                name: $('#employeeName').val(),
                employee_number: $('#employeeNumber').val(),
                national_id: $('#nationalId').val(),
                residence_issue_date: $('#issueDate').val(),
                residence_expiry_date: $('#expiryDate').val()
            };

            if (!formData.name || !formData.employee_number || !formData.national_id || !formData.residence_issue_date || !formData.residence_expiry_date) {
                showAlert('جميع الحقول مطلوبة', 'warning');
                return;
            }

            // التحقق من رقم الهوية
            if (!/^\d{10}$/.test(formData.national_id)) {
                showAlert('رقم الهوية يجب أن يكون مكون من 10 أرقام', 'warning');
                return;
            }

            if (new Date(formData.residence_expiry_date) <= new Date(formData.residence_issue_date)) {
                showAlert('تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار', 'warning');
                return;
            }

            const url = editingEmployeeId ? `/api/employees/${editingEmployeeId}` : '/api/employees';
            const method = editingEmployeeId ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                method: method,
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(data) {
                    if (data.success) {
                        showAlert(data.message, 'success');
                        $('#employeeModal').modal('hide');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showAlert(data.message, 'danger');
                    }
                }
            });
        }

        function deleteEmployee(employeeId) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                $.ajax({
                    url: `/api/employees/${employeeId}`,
                    method: 'DELETE',
                    success: function(data) {
                        if (data.success) {
                            showAlert(data.message, 'success');
                            setTimeout(() => window.location.reload(), 1000);
                        } else {
                            showAlert(data.message, 'danger');
                        }
                    }
                });
            }
        }

        function resetForm() {
            editingEmployeeId = null;
            $('#employeeForm')[0].reset();
            $('#employeeId').val('');
            $('#nationalId').removeClass('is-valid is-invalid');
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <strong>${message}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#alertContainer').html(alertHtml);
            setTimeout(() => $('.alert').alert('close'), 5000);
        }

        // دوال التصدير
        function exportToCSV() {
            const searchQuery = $('#searchInput').val();
            let url = `/export/csv?filter=${currentFilter}`;
            if (searchQuery) {
                url += `&search=${encodeURIComponent(searchQuery)}`;
            }
            window.open(url, '_blank');
        }

        function exportToPDF() {
            const searchQuery = $('#searchInput').val();
            let url = `/export/pdf?filter=${currentFilter}`;
            if (searchQuery) {
                url += `&search=${encodeURIComponent(searchQuery)}`;
            }
            window.open(url, '_blank');
        }

        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
"""

# إعداد قاعدة البيانات
def init_database():
    """تهيئة قاعدة البيانات"""
    conn = sqlite3.connect('employees_final.db')
    cursor = conn.cursor()

    # إنشاء جدول الموظفين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            employee_number TEXT UNIQUE NOT NULL,
            national_id TEXT UNIQUE NOT NULL,
            residence_issue_date DATE NOT NULL,
            residence_expiry_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # التحقق من وجود بيانات
    cursor.execute('SELECT COUNT(*) FROM employees')
    count = cursor.fetchone()[0]

    if count == 0:
        # إضافة بيانات تجريبية
        today = date.today()
        sample_data = [
            ('أحمد محمد علي', 'EMP001', '1234567890', '2023-01-15', '2025-01-15'),
            ('فاطمة أحمد', 'EMP002', '2345678901', '2023-06-10', str(today + relativedelta(days=30))),
            ('محمد عبدالله', 'EMP003', '3456789012', '2024-03-20', '2025-03-20'),
            ('سارة محمود', 'EMP004', '4567890123', '2022-08-05', str(today - relativedelta(days=30))),
            ('علي حسن', 'EMP005', '5678901234', '2024-01-01', '2026-01-01'),
            ('نور الدين', 'EMP006', '6789012345', '2023-12-15', str(today + relativedelta(days=45))),
            ('ليلى عبدالرحمن', 'EMP007', '7890123456', '2024-05-10', '2025-05-10'),
            ('خالد أحمد', 'EMP008', '8901234567', '2022-12-01', str(today - relativedelta(days=60))),
            ('منى سالم', 'EMP009', '9012345678', '2024-02-14', str(today + relativedelta(days=90))),
            ('يوسف كريم', 'EMP010', '0123456789', '2023-11-20', str(today + relativedelta(days=20)))
        ]

        cursor.executemany(
            'INSERT INTO employees (name, employee_number, national_id, residence_issue_date, residence_expiry_date) VALUES (?, ?, ?, ?, ?)',
            sample_data
        )
        print("✅ تم إنشاء البيانات التجريبية بنجاح!")

    conn.commit()
    conn.close()

def get_residence_status(expiry_date_str):
    """حساب حالة الإقامة"""
    today = date.today()
    expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()

    diff = (expiry_date - today).days

    if expiry_date < today:
        return {
            'status': 'expired',
            'label': 'منتهية',
            'class': 'danger',
            'icon': '❌',
            'days': abs(diff)
        }
    elif diff <= 60:
        return {
            'status': 'expiring',
            'label': 'قاربت على الانتهاء',
            'class': 'warning',
            'icon': '⚠️',
            'days': diff
        }
    else:
        return {
            'status': 'renewed',
            'label': 'مجددة',
            'class': 'success',
            'icon': '✅',
            'days': diff
        }

def get_employees(status_filter='all', search_query=''):
    """جلب الموظفين مع البحث والفلترة"""
    conn = sqlite3.connect('employees_final.db')
    cursor = conn.cursor()

    # بناء استعلام البحث
    if search_query:
        cursor.execute('''
            SELECT * FROM employees
            WHERE name LIKE ? OR employee_number LIKE ? OR national_id LIKE ?
            ORDER BY name
        ''', (f'%{search_query}%', f'%{search_query}%', f'%{search_query}%'))
    else:
        cursor.execute('SELECT * FROM employees ORDER BY name')

    rows = cursor.fetchall()
    conn.close()

    employees = []
    for row in rows:
        employee = {
            'id': row[0],
            'name': row[1],
            'employee_number': row[2],
            'national_id': row[3],
            'residence_issue_date': row[4],
            'residence_expiry_date': row[5],
            'created_at': row[6]
        }
        employee['status_info'] = get_residence_status(employee['residence_expiry_date'])
        employees.append(employee)

    # فلترة حسب الحالة
    if status_filter != 'all':
        employees = [emp for emp in employees if emp['status_info']['status'] == status_filter]

    return employees

def get_statistics():
    """حساب الإحصائيات"""
    employees = get_employees()

    stats = {
        'total': len(employees),
        'renewed': 0,
        'expiring': 0,
        'expired': 0
    }

    for employee in employees:
        status = employee['status_info']['status']
        stats[status] += 1

    return stats

# تهيئة قاعدة البيانات
init_database()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    status_filter = request.args.get('filter', 'all')
    search_query = request.args.get('search', '')
    employees = get_employees(status_filter, search_query)
    statistics = get_statistics()

    return render_template_string(HTML_TEMPLATE, employees=employees, statistics=statistics, request=request)

@app.route('/api/employees/<int:employee_id>')
def get_employee(employee_id):
    """جلب بيانات موظف محدد"""
    conn = sqlite3.connect('employees_final.db')
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM employees WHERE id = ?', (employee_id,))
    row = cursor.fetchone()
    conn.close()

    if row:
        employee = {
            'id': row[0],
            'name': row[1],
            'employee_number': row[2],
            'national_id': row[3],
            'residence_issue_date': row[4],
            'residence_expiry_date': row[5]
        }
        return jsonify({'success': True, 'employee': employee})
    else:
        return jsonify({'success': False, 'message': 'الموظف غير موجود'}), 404

@app.route('/api/employees', methods=['POST'])
def add_employee():
    """إضافة موظف جديد"""
    try:
        data = request.get_json()

        # التحقق من البيانات
        required_fields = ['name', 'employee_number', 'national_id', 'residence_issue_date', 'residence_expiry_date']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400

        # التحقق من رقم الهوية
        if not data['national_id'].isdigit() or len(data['national_id']) != 10:
            return jsonify({'success': False, 'message': 'رقم الهوية يجب أن يكون مكون من 10 أرقام'}), 400

        # التحقق من التواريخ
        issue_date = datetime.strptime(data['residence_issue_date'], '%Y-%m-%d').date()
        expiry_date = datetime.strptime(data['residence_expiry_date'], '%Y-%m-%d').date()

        if expiry_date <= issue_date:
            return jsonify({'success': False, 'message': 'تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار'}), 400

        conn = sqlite3.connect('employees_final.db')
        cursor = conn.cursor()

        # التحقق من عدم تكرار الرقم الوظيفي
        cursor.execute('SELECT id FROM employees WHERE employee_number = ?', (data['employee_number'],))
        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': 'الرقم الوظيفي موجود مسبقاً'}), 400

        # التحقق من عدم تكرار رقم الهوية
        cursor.execute('SELECT id FROM employees WHERE national_id = ?', (data['national_id'],))
        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': 'رقم الهوية موجود مسبقاً'}), 400

        # إضافة الموظف
        cursor.execute(
            'INSERT INTO employees (name, employee_number, national_id, residence_issue_date, residence_expiry_date) VALUES (?, ?, ?, ?, ?)',
            (data['name'], data['employee_number'], data['national_id'], data['residence_issue_date'], data['residence_expiry_date'])
        )

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'تم إضافة الموظف بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في إضافة الموظف: {str(e)}'}), 500

@app.route('/api/employees/<int:employee_id>', methods=['PUT'])
def update_employee(employee_id):
    """تحديث بيانات موظف"""
    try:
        data = request.get_json()

        # التحقق من البيانات
        required_fields = ['name', 'employee_number', 'national_id', 'residence_issue_date', 'residence_expiry_date']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'الحقل {field} مطلوب'}), 400

        # التحقق من رقم الهوية
        if not data['national_id'].isdigit() or len(data['national_id']) != 10:
            return jsonify({'success': False, 'message': 'رقم الهوية يجب أن يكون مكون من 10 أرقام'}), 400

        # التحقق من التواريخ
        issue_date = datetime.strptime(data['residence_issue_date'], '%Y-%m-%d').date()
        expiry_date = datetime.strptime(data['residence_expiry_date'], '%Y-%m-%d').date()

        if expiry_date <= issue_date:
            return jsonify({'success': False, 'message': 'تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار'}), 400

        conn = sqlite3.connect('employees_final.db')
        cursor = conn.cursor()

        # التحقق من عدم تكرار الرقم الوظيفي
        cursor.execute('SELECT id FROM employees WHERE employee_number = ? AND id != ?', (data['employee_number'], employee_id))
        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': 'الرقم الوظيفي موجود مسبقاً'}), 400

        # التحقق من عدم تكرار رقم الهوية
        cursor.execute('SELECT id FROM employees WHERE national_id = ? AND id != ?', (data['national_id'], employee_id))
        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': 'رقم الهوية موجود مسبقاً'}), 400

        # تحديث الموظف
        cursor.execute(
            'UPDATE employees SET name = ?, employee_number = ?, national_id = ?, residence_issue_date = ?, residence_expiry_date = ? WHERE id = ?',
            (data['name'], data['employee_number'], data['national_id'], data['residence_issue_date'], data['residence_expiry_date'], employee_id)
        )

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'تم تحديث بيانات الموظف بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في تحديث الموظف: {str(e)}'}), 500

@app.route('/api/employees/<int:employee_id>', methods=['DELETE'])
def delete_employee(employee_id):
    """حذف موظف"""
    try:
        conn = sqlite3.connect('employees_final.db')
        cursor = conn.cursor()

        cursor.execute('DELETE FROM employees WHERE id = ?', (employee_id,))

        if cursor.rowcount > 0:
            conn.commit()
            conn.close()
            return jsonify({'success': True, 'message': 'تم حذف الموظف بنجاح'})
        else:
            conn.close()
            return jsonify({'success': False, 'message': 'الموظف غير موجود'}), 404

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في حذف الموظف: {str(e)}'}), 500

@app.route('/export/csv')
def export_csv():
    """تصدير البيانات إلى CSV"""
    try:
        status_filter = request.args.get('filter', 'all')
        search_query = request.args.get('search', '')
        employees = get_employees(status_filter, search_query)

        # إنشاء ملف CSV في الذاكرة
        output = io.StringIO()
        writer = csv.writer(output)

        # العناوين
        writer.writerow([
            'اسم الموظف',
            'الرقم الوظيفي',
            'رقم الهوية',
            'تاريخ إصدار الإقامة',
            'تاريخ انتهاء الإقامة',
            'حالة الإقامة',
            'الأيام المتبقية',
            'تاريخ الإضافة'
        ])

        # البيانات
        for employee in employees:
            status_info = employee['status_info']

            days_text = f"{status_info['days']} يوم"
            if status_info['status'] == 'expired':
                days_text = f"منتهية منذ {status_info['days']} يوم"
            elif status_info['status'] == 'expiring':
                days_text = f"تنتهي خلال {status_info['days']} يوم"

            writer.writerow([
                employee['name'],
                employee['employee_number'],
                employee['national_id'],
                employee['residence_issue_date'],
                employee['residence_expiry_date'],
                status_info['label'],
                days_text,
                employee['created_at']
            ])

        # إنشاء الاستجابة
        output.seek(0)
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=employees_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        return response

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في تصدير CSV: {str(e)}'}), 500

@app.route('/export/pdf')
def export_pdf():
    """تصدير البيانات إلى PDF بتنسيق HTML"""
    try:
        status_filter = request.args.get('filter', 'all')
        search_query = request.args.get('search', '')
        employees = get_employees(status_filter, search_query)
        statistics = get_statistics()

        # إنشاء HTML للـ PDF
        html_content = f"""
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير الموظفين</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    direction: rtl;
                    text-align: right;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #333;
                    padding-bottom: 20px;
                }}
                .header h1 {{
                    color: #333;
                    font-size: 24px;
                    margin-bottom: 10px;
                }}
                .header p {{
                    color: #666;
                    font-size: 14px;
                }}
                .statistics {{
                    display: table;
                    width: 100%;
                    margin-bottom: 30px;
                    border-collapse: collapse;
                }}
                .stat-row {{
                    display: table-row;
                }}
                .stat-cell {{
                    display: table-cell;
                    padding: 10px;
                    border: 1px solid #ddd;
                    text-align: center;
                    background-color: #f8f9fa;
                }}
                .stat-header {{
                    background-color: #007bff;
                    color: white;
                    font-weight: bold;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: center;
                }}
                th {{
                    background-color: #007bff;
                    color: white;
                    font-weight: bold;
                }}
                tr:nth-child(even) {{
                    background-color: #f2f2f2;
                }}
                .status-renewed {{
                    background-color: #d4edda;
                    color: #155724;
                    padding: 4px 8px;
                    border-radius: 4px;
                }}
                .status-expiring {{
                    background-color: #fff3cd;
                    color: #856404;
                    padding: 4px 8px;
                    border-radius: 4px;
                }}
                .status-expired {{
                    background-color: #f8d7da;
                    color: #721c24;
                    padding: 4px 8px;
                    border-radius: 4px;
                }}
                .footer {{
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    border-top: 1px solid #ddd;
                    padding-top: 20px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🏢 تقرير نظام إدارة الموظفين</h1>
                <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        """

        # إضافة معلومات الفلترة
        if status_filter != 'all':
            filter_map = {
                'renewed': 'الإقامات المجددة',
                'expiring': 'الإقامات قاربت الانتهاء',
                'expired': 'الإقامات المنتهية'
            }
            html_content += f"<p>الفلتر: {filter_map.get(status_filter, '')}</p>"

        if search_query:
            html_content += f"<p>البحث: {search_query}</p>"

        html_content += """
            </div>

            <h2>📊 الإحصائيات العامة</h2>
            <div class="statistics">
                <div class="stat-row">
                    <div class="stat-cell stat-header">البيان</div>
                    <div class="stat-cell stat-header">العدد</div>
                </div>
        """

        # إضافة الإحصائيات
        stats_data = [
            ('إجمالي الموظفين', statistics['total']),
            ('إقامات مجددة', statistics['renewed']),
            ('قاربت على الانتهاء', statistics['expiring']),
            ('إقامات منتهية', statistics['expired'])
        ]

        for label, count in stats_data:
            html_content += f"""
                <div class="stat-row">
                    <div class="stat-cell">{label}</div>
                    <div class="stat-cell">{count}</div>
                </div>
            """

        html_content += """
            </div>

            <h2>👥 بيانات الموظفين</h2>
        """

        if employees:
            html_content += """
                <table>
                    <thead>
                        <tr>
                            <th>اسم الموظف</th>
                            <th>الرقم الوظيفي</th>
                            <th>رقم الهوية</th>
                            <th>تاريخ إصدار الإقامة</th>
                            <th>تاريخ انتهاء الإقامة</th>
                            <th>حالة الإقامة</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            for employee in employees:
                status_info = employee['status_info']
                status_class = f"status-{status_info['status']}"

                html_content += f"""
                    <tr>
                        <td>{employee['name']}</td>
                        <td>{employee['employee_number']}</td>
                        <td>{employee['national_id']}</td>
                        <td>{employee['residence_issue_date']}</td>
                        <td>{employee['residence_expiry_date']}</td>
                        <td><span class="{status_class}">{status_info['icon']} {status_info['label']}</span></td>
                    </tr>
                """

            html_content += """
                    </tbody>
                </table>
            """
        else:
            html_content += "<p>لا توجد بيانات للعرض</p>"

        html_content += """
            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين</p>
            </div>
        </body>
        </html>
        """

        # إنشاء الاستجابة
        response = make_response(html_content)
        response.headers['Content-Type'] = 'text/html; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=employees_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html'

        return response

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في تصدير PDF: {str(e)}'}), 500

if __name__ == '__main__':
    print("\n" + "="*70)
    print("🏢 نظام إدارة الموظفين النهائي - Python Flask")
    print("="*70)
    print("🚀 بدء التشغيل...")
    print("📊 الموقع متاح على: http://localhost:5000")
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("\n✨ الميزات المكتملة:")
    print("🔍 البحث بالاسم أو الرقم الوظيفي أو رقم الهوية")
    print("🆔 حقل رقم الهوية مع التحقق من الصحة")
    print("📤 تصدير CSV و PDF مع كتابة واضحة")
    print("📊 10 موظفين تجريبيين مع حالات مختلفة")
    print("📄 تقارير PDF بتنسيق HTML واضح ومقروء")
    print("🎨 واجهة عربية جميلة ومتجاوبة")
    print("="*70 + "\n")

    app.run(debug=True, host='0.0.0.0', port=5000)
