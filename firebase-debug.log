[debug] [2025-06-14T10:42:51.139Z] ----------------------------------------------------------------------
[debug] [2025-06-14T10:42:51.147Z] Command:       D:\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init
[debug] [2025-06-14T10:42:51.151Z] CLI Version:   14.7.0
[debug] [2025-06-14T10:42:51.151Z] Platform:      win32
[debug] [2025-06-14T10:42:51.152Z] Node Version:  v22.16.0
[debug] [2025-06-14T10:42:51.152Z] Time:          Sat Jun 14 2025 13:42:51 GMT+0300 (التوقيت العربي الرسمي)
[debug] [2025-06-14T10:42:51.152Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-14T10:42:51.160Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-14T10:42:51.160Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\Users\<USER>\Desktop\نظام ادارة المظفين

