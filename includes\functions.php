<?php
/**
 * الدوال المساعدة
 * Helper Functions
 */

require_once 'config/database.php';

/**
 * حساب حالة الإقامة
 * Calculate residence status
 */
function getResidenceStatus($expiry_date) {
    $today = new DateTime();
    $expiry = new DateTime($expiry_date);
    $diff = $today->diff($expiry);
    $days_diff = $diff->days;
    
    if ($expiry < $today) {
        return [
            'status' => 'expired',
            'label' => 'منتهية',
            'class' => 'status-expired',
            'icon' => '❌'
        ];
    } elseif ($days_diff <= 60) {
        return [
            'status' => 'expiring',
            'label' => 'قاربت على الانتهاء',
            'class' => 'status-expiring',
            'icon' => '⚠️'
        ];
    } else {
        return [
            'status' => 'renewed',
            'label' => 'مجددة',
            'class' => 'status-renewed',
            'icon' => '✅'
        ];
    }
}

/**
 * جلب جميع الموظفين
 * Get all employees
 */
function getAllEmployees($filter = null) {
    $conn = getDBConnection();
    
    $sql = "SELECT * FROM employees ORDER BY name ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    $employees = $stmt->fetchAll();
    
    // إضافة حالة الإقامة لكل موظف
    foreach ($employees as &$employee) {
        $employee['status_info'] = getResidenceStatus($employee['residence_expiry_date']);
    }
    
    // فلترة حسب الحالة إذا تم تحديدها
    if ($filter && $filter !== 'all') {
        $employees = array_filter($employees, function($emp) use ($filter) {
            return $emp['status_info']['status'] === $filter;
        });
    }
    
    return $employees;
}

/**
 * جلب إحصائيات الموظفين
 * Get employee statistics
 */
function getEmployeeStatistics() {
    $employees = getAllEmployees();
    
    $stats = [
        'total' => count($employees),
        'renewed' => 0,
        'expiring' => 0,
        'expired' => 0
    ];
    
    foreach ($employees as $employee) {
        $status = $employee['status_info']['status'];
        $stats[$status]++;
    }
    
    return $stats;
}

/**
 * إضافة موظف جديد
 * Add new employee
 */
function addEmployee($data) {
    $conn = getDBConnection();
    
    $sql = "INSERT INTO employees (name, employee_number, residence_issue_date, residence_expiry_date) 
            VALUES (:name, :employee_number, :residence_issue_date, :residence_expiry_date)";
    
    $stmt = $conn->prepare($sql);
    
    return $stmt->execute([
        ':name' => $data['name'],
        ':employee_number' => $data['employee_number'],
        ':residence_issue_date' => $data['residence_issue_date'],
        ':residence_expiry_date' => $data['residence_expiry_date']
    ]);
}

/**
 * تحديث بيانات موظف
 * Update employee
 */
function updateEmployee($id, $data) {
    $conn = getDBConnection();
    
    $sql = "UPDATE employees SET 
            name = :name, 
            employee_number = :employee_number, 
            residence_issue_date = :residence_issue_date, 
            residence_expiry_date = :residence_expiry_date 
            WHERE id = :id";
    
    $stmt = $conn->prepare($sql);
    
    return $stmt->execute([
        ':id' => $id,
        ':name' => $data['name'],
        ':employee_number' => $data['employee_number'],
        ':residence_issue_date' => $data['residence_issue_date'],
        ':residence_expiry_date' => $data['residence_expiry_date']
    ]);
}

/**
 * حذف موظف
 * Delete employee
 */
function deleteEmployee($id) {
    $conn = getDBConnection();
    
    $sql = "DELETE FROM employees WHERE id = :id";
    $stmt = $conn->prepare($sql);
    
    return $stmt->execute([':id' => $id]);
}

/**
 * جلب موظف بالمعرف
 * Get employee by ID
 */
function getEmployeeById($id) {
    $conn = getDBConnection();
    
    $sql = "SELECT * FROM employees WHERE id = :id";
    $stmt = $conn->prepare($sql);
    $stmt->execute([':id' => $id]);
    
    return $stmt->fetch();
}

/**
 * تنسيق التاريخ للعرض
 * Format date for display
 */
function formatDate($date) {
    return date('Y-m-d', strtotime($date));
}

/**
 * تنسيق التاريخ العربي
 * Format Arabic date
 */
function formatArabicDate($date) {
    $months = [
        '01' => 'يناير', '02' => 'فبراير', '03' => 'مارس', '04' => 'أبريل',
        '05' => 'مايو', '06' => 'يونيو', '07' => 'يوليو', '08' => 'أغسطس',
        '09' => 'سبتمبر', '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
    ];
    
    $date_parts = explode('-', $date);
    $year = $date_parts[0];
    $month = $months[$date_parts[1]];
    $day = $date_parts[2];
    
    return "$day $month $year";
}
?>
