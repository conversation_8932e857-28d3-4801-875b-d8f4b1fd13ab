<?php
/**
 * نظام إدارة الموظفين
 * Employee Management System
 */

require_once 'includes/functions.php';

// جلب الإحصائيات
$statistics = getEmployeeStatistics();
$employees = getAllEmployees();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <!-- الرأس -->
        <div class="header">
            <h1>🏢 نظام إدارة الموظفين</h1>
            <p>متابعة حالة الإقامة والبيانات الأساسية للموظفين</p>
        </div>

        <!-- الإحصائيات -->
        <div class="statistics">
            <div class="stat-card stat-total">
                <div class="icon">👥</div>
                <div class="number" id="totalCount"><?php echo $statistics['total']; ?></div>
                <div class="label">إجمالي الموظفين</div>
            </div>
            
            <div class="stat-card stat-renewed">
                <div class="icon">✅</div>
                <div class="number" id="renewedCount"><?php echo $statistics['renewed']; ?></div>
                <div class="label">إقامات مجددة</div>
            </div>
            
            <div class="stat-card stat-expiring">
                <div class="icon">⚠️</div>
                <div class="number" id="expiringCount"><?php echo $statistics['expiring']; ?></div>
                <div class="label">قاربت على الانتهاء</div>
            </div>
            
            <div class="stat-card stat-expired">
                <div class="icon">❌</div>
                <div class="number" id="expiredCount"><?php echo $statistics['expired']; ?></div>
                <div class="label">إقامات منتهية</div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="controls">
            <div>
                <button class="btn btn-primary filter-btn" data-filter="all">جميع الموظفين</button>
                <button class="btn btn-secondary filter-btn" data-filter="renewed">المجددة</button>
                <button class="btn btn-secondary filter-btn" data-filter="expiring">قاربت الانتهاء</button>
                <button class="btn btn-secondary filter-btn" data-filter="expired">المنتهية</button>
            </div>
            
            <div>
                <button class="btn btn-success" id="addEmployeeBtn">
                    ➕ إضافة موظف جديد
                </button>
            </div>
        </div>

        <!-- جدول الموظفين -->
        <div class="table-container">
            <table class="table" id="employeesTable">
                <thead>
                    <tr>
                        <th>اسم الموظف</th>
                        <th>الرقم الوظيفي</th>
                        <th>تاريخ إصدار الإقامة</th>
                        <th>تاريخ انتهاء الإقامة</th>
                        <th>حالة الإقامة</th>
                        <th>تاريخ الإضافة</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- سيتم تحميل البيانات بواسطة JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل الموظف -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="modalTitle">إضافة موظف جديد</h2>
            
            <form id="employeeForm">
                <div class="form-group">
                    <label for="employeeName">اسم الموظف:</label>
                    <input type="text" id="employeeName" name="name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="employeeNumber">الرقم الوظيفي:</label>
                    <input type="text" id="employeeNumber" name="employee_number" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="issueDate">تاريخ إصدار الإقامة:</label>
                    <input type="date" id="issueDate" name="residence_issue_date" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="expiryDate">تاريخ انتهاء الإقامة:</label>
                    <input type="date" id="expiryDate" name="residence_expiry_date" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeEmployeeModal()">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
