/**
 * نظام إدارة الموظفين - JavaScript
 * Employee Management System - JavaScript
 */

// متغيرات عامة
let currentFilter = 'all';
let editingEmployeeId = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadEmployees();
    loadStatistics();
    setupEventListeners();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // أزرار الفلترة
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            currentFilter = this.dataset.filter;
            updateFilterButtons();
            loadEmployees();
        });
    });
    
    // زر إضافة موظف
    document.getElementById('addEmployeeBtn').addEventListener('click', function() {
        openEmployeeModal();
    });
    
    // إغلاق النموذج
    document.querySelector('.close').addEventListener('click', function() {
        closeEmployeeModal();
    });
    
    // إغلاق النموذج عند النقر خارجه
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('employeeModal');
        if (event.target === modal) {
            closeEmployeeModal();
        }
    });
    
    // إرسال النموذج
    document.getElementById('employeeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveEmployee();
    });
}

// تحديث أزرار الفلترة
function updateFilterButtons() {
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-secondary');
    });
    
    document.querySelector(`[data-filter="${currentFilter}"]`).classList.remove('btn-secondary');
    document.querySelector(`[data-filter="${currentFilter}"]`).classList.add('btn-primary');
}

// تحميل قائمة الموظفين
function loadEmployees() {
    showLoading();
    
    fetch(`ajax/employee_actions.php?action=list&filter=${currentFilter}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayEmployees(data.data);
            } else {
                showAlert('خطأ في تحميل البيانات: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في الاتصال بالخادم', 'error');
            console.error('Error:', error);
        })
        .finally(() => {
            hideLoading();
        });
}

// عرض قائمة الموظفين
function displayEmployees(employees) {
    const tbody = document.querySelector('#employeesTable tbody');
    tbody.innerHTML = '';
    
    if (employees.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 50px; color: #666;">
                    لا توجد بيانات موظفين
                </td>
            </tr>
        `;
        return;
    }
    
    employees.forEach(employee => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${employee.name}</td>
            <td>${employee.employee_number}</td>
            <td>${formatDate(employee.residence_issue_date)}</td>
            <td>${formatDate(employee.residence_expiry_date)}</td>
            <td>
                <span class="status-badge ${employee.status_info.class}">
                    ${employee.status_info.icon} ${employee.status_info.label}
                </span>
            </td>
            <td>${formatDate(employee.created_at)}</td>
            <td>
                <button class="btn btn-primary btn-sm" onclick="editEmployee(${employee.id})">
                    تعديل
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteEmployee(${employee.id})">
                    حذف
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// تحميل الإحصائيات
function loadStatistics() {
    fetch('ajax/employee_actions.php?action=statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatistics(data.data);
            }
        })
        .catch(error => {
            console.error('Error loading statistics:', error);
        });
}

// تحديث الإحصائيات
function updateStatistics(stats) {
    document.getElementById('totalCount').textContent = stats.total;
    document.getElementById('renewedCount').textContent = stats.renewed;
    document.getElementById('expiringCount').textContent = stats.expiring;
    document.getElementById('expiredCount').textContent = stats.expired;
}

// فتح نموذج الموظف
function openEmployeeModal(employeeId = null) {
    editingEmployeeId = employeeId;
    const modal = document.getElementById('employeeModal');
    const form = document.getElementById('employeeForm');
    const title = document.getElementById('modalTitle');
    
    form.reset();
    
    if (employeeId) {
        title.textContent = 'تعديل بيانات الموظف';
        loadEmployeeData(employeeId);
    } else {
        title.textContent = 'إضافة موظف جديد';
    }
    
    modal.style.display = 'block';
}

// إغلاق نموذج الموظف
function closeEmployeeModal() {
    document.getElementById('employeeModal').style.display = 'none';
    editingEmployeeId = null;
}

// تحميل بيانات الموظف للتعديل
function loadEmployeeData(employeeId) {
    fetch(`ajax/employee_actions.php?action=get&id=${employeeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const employee = data.data;
                document.getElementById('employeeName').value = employee.name;
                document.getElementById('employeeNumber').value = employee.employee_number;
                document.getElementById('issueDate').value = employee.residence_issue_date;
                document.getElementById('expiryDate').value = employee.residence_expiry_date;
            } else {
                showAlert('خطأ في تحميل بيانات الموظف: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في الاتصال بالخادم', 'error');
            console.error('Error:', error);
        });
}

// حفظ بيانات الموظف
function saveEmployee() {
    const formData = new FormData();
    formData.append('action', editingEmployeeId ? 'update' : 'add');
    formData.append('name', document.getElementById('employeeName').value);
    formData.append('employee_number', document.getElementById('employeeNumber').value);
    formData.append('residence_issue_date', document.getElementById('issueDate').value);
    formData.append('residence_expiry_date', document.getElementById('expiryDate').value);
    
    if (editingEmployeeId) {
        formData.append('id', editingEmployeeId);
    }
    
    fetch('ajax/employee_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            closeEmployeeModal();
            loadEmployees();
            loadStatistics();
        } else {
            showAlert(data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('خطأ في الاتصال بالخادم', 'error');
        console.error('Error:', error);
    });
}

// تعديل موظف
function editEmployee(employeeId) {
    openEmployeeModal(employeeId);
}

// حذف موظف
function deleteEmployee(employeeId) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
        const formData = new FormData();
        formData.append('action', 'delete');
        formData.append('id', employeeId);
        
        fetch('ajax/employee_actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                loadEmployees();
                loadStatistics();
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('خطأ في الاتصال بالخادم', 'error');
            console.error('Error:', error);
        });
    }
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'error'}`;
    alertDiv.textContent = message;
    
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// عرض شاشة التحميل
function showLoading() {
    const tbody = document.querySelector('#employeesTable tbody');
    tbody.innerHTML = `
        <tr>
            <td colspan="7" class="loading">
                <div class="spinner"></div>
                جاري التحميل...
            </td>
        </tr>
    `;
}

// إخفاء شاشة التحميل
function hideLoading() {
    // يتم إخفاؤها تلقائياً عند تحميل البيانات
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}
