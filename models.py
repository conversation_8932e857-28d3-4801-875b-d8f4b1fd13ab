"""
نماذج قاعدة البيانات
Database Models
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date
from dateutil.relativedelta import relativedelta

db = SQLAlchemy()

class Employee(db.Model):
    """نموذج الموظف"""
    __tablename__ = 'employees'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False, comment='اسم الموظف')
    employee_number = db.Column(db.String(50), unique=True, nullable=False, comment='الرقم الوظيفي')
    residence_issue_date = db.Column(db.Date, nullable=False, comment='تاريخ إصدار الإقامة')
    residence_expiry_date = db.Column(db.Date, nullable=False, comment='تاريخ انتهاء الإقامة')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Employee {self.name}>'
    
    @property
    def status_info(self):
        """حساب حالة الإقامة"""
        today = date.today()
        expiry_date = self.residence_expiry_date
        
        # حساب الفرق بالأيام
        diff = (expiry_date - today).days
        
        if expiry_date < today:
            return {
                'status': 'expired',
                'label': 'منتهية',
                'class': 'danger',
                'icon': '❌',
                'days': abs(diff)
            }
        elif diff <= 60:
            return {
                'status': 'expiring',
                'label': 'قاربت على الانتهاء',
                'class': 'warning',
                'icon': '⚠️',
                'days': diff
            }
        else:
            return {
                'status': 'renewed',
                'label': 'مجددة',
                'class': 'success',
                'icon': '✅',
                'days': diff
            }
    
    @property
    def formatted_issue_date(self):
        """تنسيق تاريخ الإصدار"""
        return self.residence_issue_date.strftime('%Y-%m-%d')
    
    @property
    def formatted_expiry_date(self):
        """تنسيق تاريخ الانتهاء"""
        return self.residence_expiry_date.strftime('%Y-%m-%d')
    
    @property
    def formatted_created_at(self):
        """تنسيق تاريخ الإنشاء"""
        return self.created_at.strftime('%Y-%m-%d')
    
    def to_dict(self):
        """تحويل إلى قاموس للـ JSON"""
        return {
            'id': self.id,
            'name': self.name,
            'employee_number': self.employee_number,
            'residence_issue_date': self.formatted_issue_date,
            'residence_expiry_date': self.formatted_expiry_date,
            'created_at': self.formatted_created_at,
            'status_info': self.status_info
        }
    
    @staticmethod
    def get_statistics():
        """حساب الإحصائيات"""
        employees = Employee.query.all()
        
        stats = {
            'total': len(employees),
            'renewed': 0,
            'expiring': 0,
            'expired': 0
        }
        
        for employee in employees:
            status = employee.status_info['status']
            stats[status] += 1
        
        return stats
    
    @staticmethod
    def get_by_status(status_filter=None):
        """جلب الموظفين حسب الحالة"""
        employees = Employee.query.order_by(Employee.name).all()
        
        if status_filter and status_filter != 'all':
            employees = [emp for emp in employees if emp.status_info['status'] == status_filter]
        
        return employees
