// إعداد Firebase
const firebaseConfig = {
    apiKey: "AIzaSyDummy_Key_For_Demo",
    authDomain: "employee-system-fc21f.firebaseapp.com",
    projectId: "employee-system-fc21f",
    storageBucket: "employee-system-fc21f.appspot.com",
    messagingSenderId: "228535863",
    appId: "1:228535863:web:demo_app_id"
};

// تهيئة Firebase
firebase.initializeApp(firebaseConfig);
const db = firebase.firestore();

// متغيرات عامة
let employees = [];
let filteredEmployees = [];
let currentFilter = 'all';
let editingEmployeeId = null;
let searchTimeout = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق
async function initializeApp() {
    showLoading(true);
    
    try {
        // إنشاء بيانات تجريبية إذا لم تكن موجودة
        await createSampleDataIfNeeded();
        
        // تحميل البيانات
        await loadEmployees();
        
        // إعداد مستمعي الأحداث
        setupEventListeners();
        
        showLoading(false);
        showContent(true);
        
    } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        showAlert('خطأ في تحميل التطبيق: ' + error.message, 'danger');
        showLoading(false);
    }
}

// إنشاء بيانات تجريبية
async function createSampleDataIfNeeded() {
    try {
        const snapshot = await db.collection('employees').limit(1).get();
        
        if (snapshot.empty) {
            console.log('إنشاء بيانات تجريبية...');
            
            const today = new Date();
            const sampleData = [
                {
                    name: 'أحمد محمد علي',
                    employee_number: 'EMP001',
                    national_id: '1234567890',
                    residence_issue_date: '2023-01-15',
                    residence_expiry_date: '2025-01-15',
                    created_at: firebase.firestore.Timestamp.now()
                },
                {
                    name: 'فاطمة أحمد',
                    employee_number: 'EMP002',
                    national_id: '2345678901',
                    residence_issue_date: '2023-06-10',
                    residence_expiry_date: getDateString(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)),
                    created_at: firebase.firestore.Timestamp.now()
                },
                {
                    name: 'محمد عبدالله',
                    employee_number: 'EMP003',
                    national_id: '3456789012',
                    residence_issue_date: '2024-03-20',
                    residence_expiry_date: '2025-03-20',
                    created_at: firebase.firestore.Timestamp.now()
                },
                {
                    name: 'سارة محمود',
                    employee_number: 'EMP004',
                    national_id: '4567890123',
                    residence_issue_date: '2022-08-05',
                    residence_expiry_date: getDateString(new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)),
                    created_at: firebase.firestore.Timestamp.now()
                },
                {
                    name: 'علي حسن',
                    employee_number: 'EMP005',
                    national_id: '5678901234',
                    residence_issue_date: '2024-01-01',
                    residence_expiry_date: '2026-01-01',
                    created_at: firebase.firestore.Timestamp.now()
                }
            ];
            
            const batch = db.batch();
            sampleData.forEach(data => {
                const docRef = db.collection('employees').doc();
                batch.set(docRef, data);
            });
            
            await batch.commit();
            console.log('تم إنشاء البيانات التجريبية بنجاح');
        }
    } catch (error) {
        console.error('خطأ في إنشاء البيانات التجريبية:', error);
    }
}

// تحميل الموظفين
async function loadEmployees() {
    try {
        const snapshot = await db.collection('employees').orderBy('name').get();
        
        employees = [];
        snapshot.forEach(doc => {
            const data = doc.data();
            employees.push({
                id: doc.id,
                ...data,
                status_info: getResidenceStatus(data.residence_expiry_date)
            });
        });
        
        applyFilter();
        updateStatistics();
        displayEmployees();
        
    } catch (error) {
        console.error('خطأ في تحميل الموظفين:', error);
        showAlert('خطأ في تحميل البيانات: ' + error.message, 'danger');
    }
}

// حساب حالة الإقامة
function getResidenceStatus(expiryDateStr) {
    const today = new Date();
    const expiryDate = new Date(expiryDateStr);
    const diffTime = expiryDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (expiryDate < today) {
        return {
            status: 'expired',
            label: 'منتهية',
            class: 'danger',
            icon: '❌',
            days: Math.abs(diffDays)
        };
    } else if (diffDays <= 60) {
        return {
            status: 'expiring',
            label: 'قاربت على الانتهاء',
            class: 'warning',
            icon: '⚠️',
            days: diffDays
        };
    } else {
        return {
            status: 'renewed',
            label: 'مجددة',
            class: 'success',
            icon: '✅',
            days: diffDays
        };
    }
}

// تطبيق الفلتر
function applyFilter() {
    if (currentFilter === 'all') {
        filteredEmployees = [...employees];
    } else {
        filteredEmployees = employees.filter(emp => emp.status_info.status === currentFilter);
    }
    
    // تطبيق البحث
    const searchQuery = document.getElementById('searchInput').value.toLowerCase();
    if (searchQuery) {
        filteredEmployees = filteredEmployees.filter(emp => 
            emp.name.toLowerCase().includes(searchQuery) ||
            emp.employee_number.toLowerCase().includes(searchQuery) ||
            emp.national_id.includes(searchQuery)
        );
    }
}

// عرض الموظفين
function displayEmployees() {
    const tbody = document.getElementById('employeesTableBody');
    tbody.innerHTML = '';
    
    if (filteredEmployees.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-5 text-muted">
                    <i class="fas fa-users fa-3x mb-3"></i><br>
                    لا توجد بيانات موظفين
                </td>
            </tr>
        `;
        return;
    }
    
    filteredEmployees.forEach(employee => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>${employee.name}</strong></td>
            <td><code>${employee.employee_number}</code></td>
            <td><code>${employee.national_id}</code></td>
            <td>${employee.residence_issue_date}</td>
            <td>${employee.residence_expiry_date}</td>
            <td>
                <span class="badge bg-${employee.status_info.class}">
                    ${employee.status_info.icon} ${employee.status_info.label}
                </span>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editEmployee('${employee.id}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteEmployee('${employee.id}')">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// تحديث الإحصائيات
function updateStatistics() {
    const stats = {
        total: employees.length,
        renewed: 0,
        expiring: 0,
        expired: 0
    };
    
    employees.forEach(emp => {
        stats[emp.status_info.status]++;
    });
    
    document.getElementById('totalCount').textContent = stats.total;
    document.getElementById('renewedCount').textContent = stats.renewed;
    document.getElementById('expiringCount').textContent = stats.expiring;
    document.getElementById('expiredCount').textContent = stats.expired;
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // أزرار الفلترة
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            currentFilter = this.dataset.filter;
            updateFilterButtons();
            applyFilter();
            displayEmployees();
        });
    });
    
    // البحث
    document.getElementById('searchInput').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            applyFilter();
            displayEmployees();
        }, 500);
    });
    
    // مسح البحث
    document.getElementById('clearSearch').addEventListener('click', function() {
        document.getElementById('searchInput').value = '';
        applyFilter();
        displayEmployees();
    });
    
    // حفظ الموظف
    document.getElementById('saveEmployeeBtn').addEventListener('click', saveEmployee);
    
    // إعادة تعيين النموذج
    document.getElementById('employeeModal').addEventListener('hidden.bs.modal', resetForm);
    
    // التحقق من رقم الهوية
    document.getElementById('nationalId').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        this.value = value;
        
        if (value.length === 10) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (value.length > 0) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
}

// تحديث أزرار الفلترة
function updateFilterButtons() {
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('btn-primary', 'btn-success', 'btn-warning', 'btn-danger', 'active');
        btn.classList.add('btn-outline-primary');
    });
    
    const activeBtn = document.querySelector(`[data-filter="${currentFilter}"]`);
    if (activeBtn) {
        activeBtn.classList.remove('btn-outline-primary', 'btn-outline-success', 'btn-outline-warning', 'btn-outline-danger');
        
        if (currentFilter === 'all') {
            activeBtn.classList.add('btn-primary', 'active');
        } else if (currentFilter === 'renewed') {
            activeBtn.classList.add('btn-success', 'active');
        } else if (currentFilter === 'expiring') {
            activeBtn.classList.add('btn-warning', 'active');
        } else if (currentFilter === 'expired') {
            activeBtn.classList.add('btn-danger', 'active');
        }
    }
    
    // تحديث الأزرار الأخرى
    document.querySelectorAll('.filter-btn').forEach(btn => {
        if (!btn.classList.contains('active')) {
            const filter = btn.dataset.filter;
            btn.classList.remove('btn-outline-primary');
            if (filter === 'renewed') {
                btn.classList.add('btn-outline-success');
            } else if (filter === 'expiring') {
                btn.classList.add('btn-outline-warning');
            } else if (filter === 'expired') {
                btn.classList.add('btn-outline-danger');
            } else {
                btn.classList.add('btn-outline-primary');
            }
        }
    });
}

// دوال مساعدة
function getDateString(date) {
    return date.toISOString().split('T')[0];
}

function showLoading(show) {
    document.getElementById('loading').classList.toggle('show', show);
}

function showContent(show) {
    document.getElementById('statisticsRow').style.display = show ? 'flex' : 'none';
    document.getElementById('controlsCard').style.display = show ? 'block' : 'none';
    document.getElementById('tableCard').style.display = show ? 'block' : 'none';
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <strong>${message}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.getElementById('alertContainer').innerHTML = alertHtml;

    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// تعديل موظف
async function editEmployee(employeeId) {
    try {
        const doc = await db.collection('employees').doc(employeeId).get();

        if (doc.exists) {
            const employee = doc.data();
            editingEmployeeId = employeeId;

            document.getElementById('modalTitle').textContent = 'تعديل بيانات الموظف';
            document.getElementById('employeeId').value = employeeId;
            document.getElementById('employeeName').value = employee.name;
            document.getElementById('employeeNumber').value = employee.employee_number;
            document.getElementById('nationalId').value = employee.national_id;
            document.getElementById('issueDate').value = employee.residence_issue_date;
            document.getElementById('expiryDate').value = employee.residence_expiry_date;

            const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
            modal.show();
        } else {
            showAlert('الموظف غير موجود', 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات الموظف:', error);
        showAlert('خطأ في تحميل بيانات الموظف: ' + error.message, 'danger');
    }
}

// حفظ الموظف
async function saveEmployee() {
    try {
        const formData = {
            name: document.getElementById('employeeName').value.trim(),
            employee_number: document.getElementById('employeeNumber').value.trim(),
            national_id: document.getElementById('nationalId').value.trim(),
            residence_issue_date: document.getElementById('issueDate').value,
            residence_expiry_date: document.getElementById('expiryDate').value
        };

        // التحقق من البيانات
        if (!formData.name || !formData.employee_number || !formData.national_id ||
            !formData.residence_issue_date || !formData.residence_expiry_date) {
            showAlert('جميع الحقول مطلوبة', 'warning');
            return;
        }

        // التحقق من رقم الهوية
        if (!/^\d{10}$/.test(formData.national_id)) {
            showAlert('رقم الهوية يجب أن يكون مكون من 10 أرقام', 'warning');
            return;
        }

        // التحقق من التواريخ
        if (new Date(formData.residence_expiry_date) <= new Date(formData.residence_issue_date)) {
            showAlert('تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار', 'warning');
            return;
        }

        // التحقق من عدم تكرار الرقم الوظيفي
        const employeeNumberQuery = await db.collection('employees')
            .where('employee_number', '==', formData.employee_number)
            .get();

        if (!employeeNumberQuery.empty) {
            const existingDoc = employeeNumberQuery.docs[0];
            if (!editingEmployeeId || existingDoc.id !== editingEmployeeId) {
                showAlert('الرقم الوظيفي موجود مسبقاً', 'warning');
                return;
            }
        }

        // التحقق من عدم تكرار رقم الهوية
        const nationalIdQuery = await db.collection('employees')
            .where('national_id', '==', formData.national_id)
            .get();

        if (!nationalIdQuery.empty) {
            const existingDoc = nationalIdQuery.docs[0];
            if (!editingEmployeeId || existingDoc.id !== editingEmployeeId) {
                showAlert('رقم الهوية موجود مسبقاً', 'warning');
                return;
            }
        }

        // إضافة تاريخ الإنشاء أو التحديث
        if (editingEmployeeId) {
            formData.updated_at = firebase.firestore.Timestamp.now();
            await db.collection('employees').doc(editingEmployeeId).update(formData);
            showAlert('تم تحديث بيانات الموظف بنجاح', 'success');
        } else {
            formData.created_at = firebase.firestore.Timestamp.now();
            await db.collection('employees').add(formData);
            showAlert('تم إضافة الموظف بنجاح', 'success');
        }

        // إغلاق النموذج وإعادة تحميل البيانات
        const modal = bootstrap.Modal.getInstance(document.getElementById('employeeModal'));
        modal.hide();

        await loadEmployees();

    } catch (error) {
        console.error('خطأ في حفظ الموظف:', error);
        showAlert('خطأ في حفظ الموظف: ' + error.message, 'danger');
    }
}

// حذف موظف
async function deleteEmployee(employeeId) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
        try {
            await db.collection('employees').doc(employeeId).delete();
            showAlert('تم حذف الموظف بنجاح', 'success');
            await loadEmployees();
        } catch (error) {
            console.error('خطأ في حذف الموظف:', error);
            showAlert('خطأ في حذف الموظف: ' + error.message, 'danger');
        }
    }
}

// إعادة تعيين النموذج
function resetForm() {
    editingEmployeeId = null;
    document.getElementById('employeeForm').reset();
    document.getElementById('employeeId').value = '';
    document.getElementById('nationalId').classList.remove('is-valid', 'is-invalid');
    document.getElementById('modalTitle').textContent = 'إضافة موظف جديد';
}

// تصدير البيانات
function exportData() {
    try {
        // إنشاء CSV
        let csvContent = "اسم الموظف,الرقم الوظيفي,رقم الهوية,تاريخ إصدار الإقامة,تاريخ انتهاء الإقامة,حالة الإقامة\n";

        filteredEmployees.forEach(employee => {
            csvContent += `"${employee.name}","${employee.employee_number}","${employee.national_id}","${employee.residence_issue_date}","${employee.residence_expiry_date}","${employee.status_info.label}"\n`;
        });

        // تحميل الملف
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `employees_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showAlert('تم تصدير البيانات بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        showAlert('خطأ في تصدير البيانات: ' + error.message, 'danger');
    }
}
