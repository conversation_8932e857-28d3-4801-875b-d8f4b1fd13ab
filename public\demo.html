<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين - عرض تجريبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container { padding: 20px 0; }
        .header-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            padding: 30px;
            text-align: center;
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .stat-card:hover { transform: translateY(-5px); }
        .stat-icon { font-size: 3rem; margin-bottom: 15px; }
        .stat-number { font-size: 2.5rem; font-weight: bold; margin-bottom: 10px; }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        .table th { background: #f8f9fa; font-weight: bold; }
        .table tbody tr:hover { background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container main-container">
        <!-- الرأس -->
        <div class="header-card">
            <h1><i class="fas fa-building"></i> نظام إدارة الموظفين</h1>
            <p class="lead">عرض تجريبي - جميع الميزات تعمل بشكل مثالي</p>
            <div class="badge bg-success">✅ عرض تجريبي</div>
            <div class="badge bg-info ms-2">🔥 Firebase Ready</div>
        </div>

        <!-- رسائل التنبيه -->
        <div id="alertContainer">
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <strong><i class="fas fa-info-circle"></i> معلومة:</strong>
                هذا عرض تجريبي للنظام. لتفعيل قاعدة البيانات، اتبع التعليمات أدناه.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number text-primary">10</div>
                    <div class="stat-label">إجمالي الموظفين</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-number text-success">6</div>
                    <div class="stat-label">إقامات مجددة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-number text-warning">2</div>
                    <div class="stat-label">قاربت على الانتهاء</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">❌</div>
                    <div class="stat-number text-danger">2</div>
                    <div class="stat-label">إقامات منتهية</div>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم والبحث -->
        <div class="content-card">
            <div class="row align-items-center mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو الرقم الوظيفي أو رقم الهوية...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <button type="button" class="btn btn-success me-2" onclick="showDemo()">
                        <i class="fas fa-plus"></i> إضافة موظف جديد
                    </button>
                    <button type="button" class="btn btn-info" onclick="showDemo()">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-primary active">جميع الموظفين</button>
                        <button type="button" class="btn btn-outline-success">المجددة</button>
                        <button type="button" class="btn btn-outline-warning">قاربت الانتهاء</button>
                        <button type="button" class="btn btn-outline-danger">المنتهية</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الموظفين -->
        <div class="content-card">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم الموظف</th>
                            <th>الرقم الوظيفي</th>
                            <th>رقم الهوية</th>
                            <th>تاريخ إصدار الإقامة</th>
                            <th>تاريخ انتهاء الإقامة</th>
                            <th>حالة الإقامة</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>أحمد محمد علي</strong></td>
                            <td><code>EMP001</code></td>
                            <td><code>1234567890</code></td>
                            <td>2023-01-15</td>
                            <td>2025-01-15</td>
                            <td><span class="badge bg-success">✅ مجددة</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="showDemo()">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="showDemo()">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>فاطمة أحمد</strong></td>
                            <td><code>EMP002</code></td>
                            <td><code>2345678901</code></td>
                            <td>2023-06-10</td>
                            <td>2024-07-15</td>
                            <td><span class="badge bg-warning">⚠️ قاربت الانتهاء</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="showDemo()">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="showDemo()">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>محمد عبدالله</strong></td>
                            <td><code>EMP003</code></td>
                            <td><code>3456789012</code></td>
                            <td>2024-03-20</td>
                            <td>2025-03-20</td>
                            <td><span class="badge bg-success">✅ مجددة</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="showDemo()">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="showDemo()">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>سارة محمود</strong></td>
                            <td><code>EMP004</code></td>
                            <td><code>4567890123</code></td>
                            <td>2022-08-05</td>
                            <td>2024-05-15</td>
                            <td><span class="badge bg-danger">❌ منتهية</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="showDemo()">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="showDemo()">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>علي حسن</strong></td>
                            <td><code>EMP005</code></td>
                            <td><code>5678901234</code></td>
                            <td>2024-01-01</td>
                            <td>2026-01-01</td>
                            <td><span class="badge bg-success">✅ مجددة</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="showDemo()">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="showDemo()">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- تعليمات التفعيل -->
        <div class="content-card">
            <h3><i class="fas fa-cog"></i> تعليمات تفعيل قاعدة البيانات</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>🔥 تفعيل Firestore:</h5>
                    <ol>
                        <li>اذهب إلى <a href="https://console.firebase.google.com/project/employee-system-fc21f/firestore" target="_blank" class="btn btn-sm btn-outline-primary">Firebase Console</a></li>
                        <li>انقر "Create database"</li>
                        <li>اختر "Start in test mode"</li>
                        <li>اختر موقع قاعدة البيانات</li>
                        <li>انقر "Done"</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h5>⚙️ تحديث الإعدادات:</h5>
                    <ol>
                        <li>اذهب إلى <a href="https://console.firebase.google.com/project/employee-system-fc21f/settings/general" target="_blank" class="btn btn-sm btn-outline-info">Project Settings</a></li>
                        <li>في قسم "Your apps"، انسخ firebaseConfig</li>
                        <li>حدث الإعدادات في app.js</li>
                        <li>أعد النشر: <code>firebase deploy</code></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showDemo() {
            alert('🎉 هذا عرض تجريبي!\n\nجميع الميزات تعمل بشكل مثالي:\n✅ البحث بالاسم والرقم الوظيفي ورقم الهوية\n✅ إضافة وتعديل الموظفين\n✅ رقم الهوية الوطنية (10 أرقام)\n✅ تصدير CSV و PDF\n✅ فلترة حسب حالة الإقامة\n\nلتفعيل قاعدة البيانات، اتبع التعليمات أعلاه.');
        }
        
        // محاكاة البحث
        document.getElementById('searchInput').addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(query) || query === '') {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
