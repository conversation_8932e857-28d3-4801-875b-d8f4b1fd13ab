<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين - Firebase</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container { padding: 20px 0; }
        .header-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            padding: 30px;
            text-align: center;
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .stat-card:hover { transform: translateY(-5px); }
        .stat-icon { font-size: 3rem; margin-bottom: 15px; }
        .stat-number { font-size: 2.5rem; font-weight: bold; margin-bottom: 10px; }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        .table th { background: #f8f9fa; font-weight: bold; }
        .table tbody tr:hover { background: #f8f9fa; }
        .loading { display: none; text-align: center; padding: 50px; }
        .loading.show { display: block; }
    </style>
</head>
<body>
    <div class="container main-container">
        <!-- الرأس -->
        <div class="header-card">
            <h1><i class="fas fa-building"></i> نظام إدارة الموظفين</h1>
            <p class="lead">متابعة حالة الإقامة والبيانات الأساسية للموظفين - مدعوم بـ Firebase</p>
            <div class="badge bg-success">🔥 Firebase Hosting</div>
        </div>

        <!-- رسائل التنبيه -->
        <div id="alertContainer"></div>

        <!-- Loading -->
        <div id="loading" class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3">جاري تحميل البيانات من Firebase...</p>
        </div>

        <!-- الإحصائيات -->
        <div class="row" id="statisticsRow" style="display: none;">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number text-primary" id="totalCount">0</div>
                    <div class="stat-label">إجمالي الموظفين</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-number text-success" id="renewedCount">0</div>
                    <div class="stat-label">إقامات مجددة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-number text-warning" id="expiringCount">0</div>
                    <div class="stat-label">قاربت على الانتهاء</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">❌</div>
                    <div class="stat-number text-danger" id="expiredCount">0</div>
                    <div class="stat-label">إقامات منتهية</div>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم والبحث -->
        <div class="content-card" id="controlsCard" style="display: none;">
            <div class="row align-items-center mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو الرقم الوظيفي أو رقم الهوية...">
                        <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#employeeModal">
                        <i class="fas fa-plus"></i> إضافة موظف جديد
                    </button>
                    <button type="button" class="btn btn-info" onclick="exportData()">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-primary filter-btn active" data-filter="all">جميع الموظفين</button>
                        <button type="button" class="btn btn-outline-success filter-btn" data-filter="renewed">المجددة</button>
                        <button type="button" class="btn btn-outline-warning filter-btn" data-filter="expiring">قاربت الانتهاء</button>
                        <button type="button" class="btn btn-outline-danger filter-btn" data-filter="expired">المنتهية</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الموظفين -->
        <div class="content-card" id="tableCard" style="display: none;">
            <div class="table-responsive">
                <table class="table table-hover" id="employeesTable">
                    <thead>
                        <tr>
                            <th>اسم الموظف</th>
                            <th>الرقم الوظيفي</th>
                            <th>رقم الهوية</th>
                            <th>تاريخ إصدار الإقامة</th>
                            <th>تاريخ انتهاء الإقامة</th>
                            <th>حالة الإقامة</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody id="employeesTableBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- نموذج إضافة/تعديل الموظف -->
        <div class="modal fade" id="employeeModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalTitle">إضافة موظف جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="employeeForm">
                            <input type="hidden" id="employeeId" name="id">
                            <div class="mb-3">
                                <label for="employeeName" class="form-label">اسم الموظف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="employeeName" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="employeeNumber" class="form-label">الرقم الوظيفي <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="employeeNumber" name="employee_number" required>
                            </div>
                            <div class="mb-3">
                                <label for="nationalId" class="form-label">رقم الهوية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nationalId" name="national_id" required 
                                       placeholder="مثال: 1234567890" maxlength="10" pattern="[0-9]{10}">
                                <div class="form-text">يجب أن يكون رقم الهوية مكون من 10 أرقام</div>
                            </div>
                            <div class="mb-3">
                                <label for="issueDate" class="form-label">تاريخ إصدار الإقامة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="issueDate" name="residence_issue_date" required>
                            </div>
                            <div class="mb-3">
                                <label for="expiryDate" class="form-label">تاريخ انتهاء الإقامة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="expiryDate" name="residence_expiry_date" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" id="saveEmployeeBtn">حفظ</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تطبيق JavaScript -->
    <script src="app.js"></script>
</body>
</html>
