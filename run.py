#!/usr/bin/env python3
"""
تشغيل نظام إدارة الموظفين
Run Employee Management System
"""

import sys
import subprocess
import os

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح!")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def run_app():
    """تشغيل التطبيق"""
    print("\n" + "="*50)
    print("🏢 نظام إدارة الموظفين")
    print("="*50)
    print("🚀 بدء التشغيل...")
    print("📊 الموقع سيكون متاح على: http://localhost:5000")
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("="*50 + "\n")
    
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص النظام...")
    
    # التحقق من إصدار Python
    if not check_python_version():
        return
    
    # التحقق من وجود ملف المتطلبات
    if not os.path.exists("requirements.txt"):
        print("❌ ملف requirements.txt غير موجود")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        return
    
    # تشغيل التطبيق
    run_app()

if __name__ == "__main__":
    main()
