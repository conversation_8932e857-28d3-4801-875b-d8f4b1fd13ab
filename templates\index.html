{% extends "base.html" %}

{% block title %}نظام إدارة الموظفين{% endblock %}

{% block content %}
<!-- الرأس -->
<div class="header-card">
    <h1><i class="fas fa-building"></i> نظام إدارة الموظفين</h1>
    <p class="lead">متابعة حالة الإقامة والبيانات الأساسية للموظفين</p>
</div>

<!-- رسائل التنبيه -->
<div id="alertContainer"></div>

<!-- الإحصائيات -->
<div class="row" id="statisticsRow">
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-number text-primary" id="totalCount">{{ statistics.total }}</div>
            <div class="stat-label">إجمالي الموظفين</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-number text-success" id="renewedCount">{{ statistics.renewed }}</div>
            <div class="stat-label">إقامات مجددة</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon">⚠️</div>
            <div class="stat-number text-warning" id="expiringCount">{{ statistics.expiring }}</div>
            <div class="stat-label">قاربت على الانتهاء</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="stat-icon">❌</div>
            <div class="stat-number text-danger" id="expiredCount">{{ statistics.expired }}</div>
            <div class="stat-label">إقامات منتهية</div>
        </div>
    </div>
</div>

<!-- أزرار التحكم -->
<div class="content-card">
    <div class="row align-items-center">
        <div class="col-md-8">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-primary btn-custom filter-btn active" data-filter="all">
                    جميع الموظفين
                </button>
                <button type="button" class="btn btn-outline-success btn-custom filter-btn" data-filter="renewed">
                    المجددة
                </button>
                <button type="button" class="btn btn-outline-warning btn-custom filter-btn" data-filter="expiring">
                    قاربت الانتهاء
                </button>
                <button type="button" class="btn btn-outline-danger btn-custom filter-btn" data-filter="expired">
                    المنتهية
                </button>
            </div>
        </div>
        
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-success btn-custom" data-bs-toggle="modal" data-bs-target="#employeeModal">
                <i class="fas fa-plus"></i> إضافة موظف جديد
            </button>
        </div>
    </div>
</div>

<!-- جدول الموظفين -->
<div class="content-card">
    <div class="table-responsive">
        <table class="table table-hover" id="employeesTable">
            <thead>
                <tr>
                    <th>اسم الموظف</th>
                    <th>الرقم الوظيفي</th>
                    <th>تاريخ إصدار الإقامة</th>
                    <th>تاريخ انتهاء الإقامة</th>
                    <th>حالة الإقامة</th>
                    <th>تاريخ الإضافة</th>
                    <th>العمليات</th>
                </tr>
            </thead>
            <tbody id="employeesTableBody">
                <!-- سيتم تحميل البيانات بواسطة JavaScript -->
            </tbody>
        </table>
    </div>
</div>

<!-- نموذج إضافة/تعديل الموظف -->
<div class="modal fade" id="employeeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">إضافة موظف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="employeeForm">
                    <input type="hidden" id="employeeId" name="id">
                    
                    <div class="mb-3">
                        <label for="employeeName" class="form-label">اسم الموظف <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="employeeName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="employeeNumber" class="form-label">الرقم الوظيفي <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="employeeNumber" name="employee_number" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="issueDate" class="form-label">تاريخ إصدار الإقامة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="issueDate" name="residence_issue_date" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="expiryDate" class="form-label">تاريخ انتهاء الإقامة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="expiryDate" name="residence_expiry_date" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveEmployeeBtn">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let currentFilter = 'all';
let editingEmployeeId = null;

// تحميل الصفحة
$(document).ready(function() {
    loadEmployees();
    setupEventListeners();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // أزرار الفلترة
    $('.filter-btn').click(function() {
        currentFilter = $(this).data('filter');
        updateFilterButtons();
        loadEmployees();
    });
    
    // حفظ الموظف
    $('#saveEmployeeBtn').click(function() {
        saveEmployee();
    });
    
    // إعادة تعيين النموذج عند إغلاقه
    $('#employeeModal').on('hidden.bs.modal', function() {
        resetForm();
    });
}

// تحديث أزرار الفلترة
function updateFilterButtons() {
    $('.filter-btn').removeClass('btn-primary btn-outline-success btn-outline-warning btn-outline-danger active');
    
    const activeBtn = $(`.filter-btn[data-filter="${currentFilter}"]`);
    
    if (currentFilter === 'all') {
        activeBtn.addClass('btn-primary active');
    } else if (currentFilter === 'renewed') {
        activeBtn.addClass('btn-success active');
    } else if (currentFilter === 'expiring') {
        activeBtn.addClass('btn-warning active');
    } else if (currentFilter === 'expired') {
        activeBtn.addClass('btn-danger active');
    }
    
    // تحديث الأزرار الأخرى
    $('.filter-btn').not(activeBtn).each(function() {
        const filter = $(this).data('filter');
        if (filter === 'renewed') {
            $(this).addClass('btn-outline-success');
        } else if (filter === 'expiring') {
            $(this).addClass('btn-outline-warning');
        } else if (filter === 'expired') {
            $(this).addClass('btn-outline-danger');
        } else {
            $(this).addClass('btn-outline-primary');
        }
    });
}

// تحميل قائمة الموظفين
function loadEmployees() {
    $.get(`/api/employees?filter=${currentFilter}`)
        .done(function(data) {
            if (data.success) {
                displayEmployees(data.employees);
                updateStatistics(data.statistics);
            } else {
                showAlert('خطأ في تحميل البيانات: ' + data.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        });
}

// عرض قائمة الموظفين
function displayEmployees(employees) {
    const tbody = $('#employeesTableBody');
    tbody.empty();
    
    if (employees.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center py-5 text-muted">
                    <i class="fas fa-users fa-3x mb-3"></i><br>
                    لا توجد بيانات موظفين
                </td>
            </tr>
        `);
        return;
    }
    
    employees.forEach(function(employee) {
        const statusClass = employee.status_info.class;
        const statusIcon = employee.status_info.icon;
        const statusLabel = employee.status_info.label;
        
        const row = `
            <tr>
                <td><strong>${employee.name}</strong></td>
                <td><code>${employee.employee_number}</code></td>
                <td>${employee.residence_issue_date}</td>
                <td>${employee.residence_expiry_date}</td>
                <td>
                    <span class="badge bg-${statusClass} status-badge">
                        ${statusIcon} ${statusLabel}
                    </span>
                </td>
                <td>${employee.created_at}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editEmployee(${employee.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteEmployee(${employee.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// تحديث الإحصائيات
function updateStatistics(stats) {
    $('#totalCount').text(stats.total);
    $('#renewedCount').text(stats.renewed);
    $('#expiringCount').text(stats.expiring);
    $('#expiredCount').text(stats.expired);
}

// فتح نموذج إضافة موظف
function openAddModal() {
    resetForm();
    $('#modalTitle').text('إضافة موظف جديد');
    $('#employeeModal').modal('show');
}

// تعديل موظف
function editEmployee(employeeId) {
    $.get(`/api/employees/${employeeId}`)
        .done(function(data) {
            if (data.success) {
                const employee = data.employee;
                editingEmployeeId = employeeId;
                
                $('#modalTitle').text('تعديل بيانات الموظف');
                $('#employeeId').val(employee.id);
                $('#employeeName').val(employee.name);
                $('#employeeNumber').val(employee.employee_number);
                $('#issueDate').val(employee.residence_issue_date);
                $('#expiryDate').val(employee.residence_expiry_date);
                
                $('#employeeModal').modal('show');
            } else {
                showAlert('خطأ في تحميل بيانات الموظف: ' + data.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        });
}

// حفظ الموظف
function saveEmployee() {
    const formData = {
        name: $('#employeeName').val(),
        employee_number: $('#employeeNumber').val(),
        residence_issue_date: $('#issueDate').val(),
        residence_expiry_date: $('#expiryDate').val()
    };
    
    // التحقق من صحة البيانات
    if (!formData.name || !formData.employee_number || !formData.residence_issue_date || !formData.residence_expiry_date) {
        showAlert('جميع الحقول مطلوبة', 'warning');
        return;
    }
    
    // التحقق من أن تاريخ الانتهاء بعد تاريخ الإصدار
    if (new Date(formData.residence_expiry_date) <= new Date(formData.residence_issue_date)) {
        showAlert('تاريخ انتهاء الإقامة يجب أن يكون بعد تاريخ الإصدار', 'warning');
        return;
    }
    
    const url = editingEmployeeId ? `/api/employees/${editingEmployeeId}` : '/api/employees';
    const method = editingEmployeeId ? 'PUT' : 'POST';
    
    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(data) {
            if (data.success) {
                showAlert(data.message, 'success');
                $('#employeeModal').modal('hide');
                loadEmployees();
            } else {
                showAlert(data.message, 'danger');
            }
        },
        error: function() {
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    });
}

// حذف موظف
function deleteEmployee(employeeId) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
        $.ajax({
            url: `/api/employees/${employeeId}`,
            method: 'DELETE',
            success: function(data) {
                if (data.success) {
                    showAlert(data.message, 'success');
                    loadEmployees();
                } else {
                    showAlert(data.message, 'danger');
                }
            },
            error: function() {
                showAlert('خطأ في الاتصال بالخادم', 'danger');
            }
        });
    }
}

// إعادة تعيين النموذج
function resetForm() {
    editingEmployeeId = null;
    $('#employeeForm')[0].reset();
    $('#employeeId').val('');
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <strong>${message}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('#alertContainer').html(alertHtml);
    
    // إخفاء التنبيه تلقائياً بعد 5 ثوان
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
