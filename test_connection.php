<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 */

echo "<h2>🔍 اختبار الاتصال بقاعدة البيانات</h2>";

try {
    require_once 'config/database.php';
    
    $conn = getDBConnection();
    
    if ($conn) {
        echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</p>";
        
        // اختبار وجود جدول الموظفين
        $stmt = $conn->prepare("SHOW TABLES LIKE 'employees'");
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ جدول الموظفين موجود!</p>";
            
            // عد الموظفين
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM employees");
            $stmt->execute();
            $result = $stmt->fetch();
            
            echo "<p style='color: blue;'>📊 عدد الموظفين في قاعدة البيانات: " . $result['count'] . "</p>";
            
        } else {
            echo "<p style='color: orange;'>⚠️ جدول الموظفين غير موجود. يرجى تشغيل ملف database.sql</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ فشل في الاتصال بقاعدة البيانات!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>📋 معلومات النظام:</h3>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . phpversion() . "</li>";
echo "<li><strong>إصدار MySQL:</strong> " . (isset($conn) ? $conn->getAttribute(PDO::ATTR_SERVER_VERSION) : 'غير متصل') . "</li>";
echo "<li><strong>الترميز:</strong> UTF-8</li>";
echo "</ul>";

echo "<hr>";
echo "<p><a href='index.php' style='background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 العودة للصفحة الرئيسية</a></p>";
?>
